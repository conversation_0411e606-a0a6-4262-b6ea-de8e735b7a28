<!--
  @description       :
  <AUTHOR> Aryan <PERSON>
  @group             :
  @last modified on  : 07-01-2025
  @last modified by  : Hari
-->
<template>

    <lightning-layout multiple-rows vertical-align="stretch">

        <template if:false={showUnpackRequestErrorMessage}>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                flexibility="auto" multiple-rows if:false={showReadOnly}>
                    <lightning-combobox data-id={data.id} data-fieldlabel="selectedIssueCategory" data-type="combobox"
                        name="issueCategory" label="Select Issue Category" value={data.selectedIssueCategory}
                        placeholder="Select Issue Category" options={issueCategoryOptions} onchange={handleIssueCategoryChange}
                        required>
                    </lightning-combobox>
            </lightning-layout-item>
        </template>
        <template if:true={showUnpackRequestErrorMessage}>
            <span class="slds-p-top_large" style="color:red"><b>Error: You cannot create a claim for this item. Please proceed with an unpack request instead.</b></span>
        </template>


        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
            flexibility="auto" multiple-rows>
            <!-- Change of Mind Category -->
            <template lwc:if={isIssueCategoryIsChangeOfMind}>
                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                    flexibility="auto" multiple-rows>
                    <c-claim-form-change-of-mind cp-obj={data} full-card-data={cardData} erp-data={erpData}
                        ondatachange={handleDataChange} approval-screen={approvalScreen}></c-claim-form-change-of-mind>
                </lightning-layout-item>
            </template>

            <!-- Product Issue Category -->
            <template lwc:if={isIssueCategoryIsProductIssue}>
                <template lwc:if={isSKUListedAsRecalled}>

                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows>
                        <lightning-combobox data-id={data.id} data-fieldlabel="selectedIssueSubCategory"
                            data-type="combobox" name="selectedIssueSubCategory" label="Select an option"
                            value={data.selectedIssueSubCategory} placeholder="Select an option"
                            options={productIssueProductRecallSubCategoryOptions} onchange={handleChange} required
                            read-only={approvalScreen} disabled={approvalScreen}>{approvalScreen}
                        </lightning-combobox>
                    </lightning-layout-item>

                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows>
                        <lightning-layout multiple-rows vertical-align="stretch">
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                large-device-size="6" flexibility="auto" multiple-rows>
                                <template if:true={data.serialVisible}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            <template if:true={data.serialRequired}>
                                                <abbr class="slds-required" title="required">* </abbr>
                                            </template>
                                            {data.serialLabel} <lightning-button-icon icon-name="utility:info"
                                                variant="bare" alternative-text="Info" onclick={navigateURL}>
                                            </lightning-button-icon>
                                        </label>
                                    </div>
                                    <template if:true={data.serialRequired}>
                                        <lightning-input data-id={data.id} data-type="input"
                                            data-fieldlabel={data.serialLabel} label={data.serialLabel}
                                            value={data.serialValue} variant="label-hidden" onchange={handleChange}
                                            required={data.serialRequired} read-only={approvalScreen}>
                                        </lightning-input>
                                    </template>
                                    <template if:false={data.serialRequired}>
                                        <lightning-input data-id={data.id} data-type="input"
                                            data-fieldlabel={data.serialLabel} label={data.serialLabel}
                                            value={data.serialValue} variant="label-hidden" onchange={handleChange}
                                            required={data.serialRequired} read-only>
                                        </lightning-input>
                                    </template>

                                </template>
                                <template if:false={data.serialVisible}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            {data.serialLabel} <lightning-button-icon icon-name="utility:info"
                                                variant="bare" alternative-text="Info" onclick={navigateURL}>
                                            </lightning-button-icon>
                                        </label>
                                        <div class="slds-form-element__control">
                                            N/A
                                        </div>
                                    </div>
                                </template>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                large-device-size="6" flexibility="auto" multiple-rows>
                                <template if:true={data.batchVisible}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            <template if:true={data.batchRequired}>
                                                <abbr class="slds-required" title="required">* </abbr>
                                            </template>
                                            {data.batchLabel} <lightning-button-icon icon-name="utility:info"
                                                variant="bare" alternative-text="Info" onclick={navigateURL}>
                                            </lightning-button-icon>
                                        </label>
                                    </div>
                                    <template if:true={data.batchRequired}>
                                        <lightning-input data-id={data.id} data-type="input"
                                            data-fieldlabel={data.batchLabel} label={data.batchLabel}
                                            value={data.batchValue} variant="label-hidden" onchange={handleChange}
                                            required={data.batchRequired} read-only={approvalScreen}>
                                        </lightning-input>
                                    </template>
                                    <template if:false={data.batchRequired}>
                                        <lightning-input data-id={data.id} data-type="input"
                                            data-fieldlabel={data.batchLabel} label={data.batchLabel}
                                            value={data.batchValue} variant="label-hidden" onchange={handleChange}
                                            required={data.batchRequired} read-only>
                                        </lightning-input>
                                    </template>

                                </template>
                                <template if:false={data.batchVisible}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            {data.batchLabel} <lightning-button-icon icon-name="utility:info"
                                                variant="bare" alternative-text="Info" onclick={navigateURL}>
                                            </lightning-button-icon>
                                        </label>
                                        <div class="slds-form-element__control">
                                            N/A
                                        </div>
                                    </div>
                                </template>
                            </lightning-layout-item>
                        </lightning-layout>
                    </lightning-layout-item>


                    <template if:false={approvalScreen}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} has-custom-field={trueVal}
                                custom-field-name="File_category_fileupload__c" custom-field-value="Product Issue Photo"
                                onupdateddata={linkFilesToCard} input-label="Product Issue Photo">
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} has-custom-field={trueVal}
                                custom-field-name="File_category_fileupload__c"
                                custom-field-value="Batch Location Photo" onupdateddata={linkFilesToCard}
                                input-label="Batch Location Photo">
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                    </template>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="12" flexibility="auto" multiple-rows>
                        <template if:true={isFilesUploaded}>
                            <div class="slds-form-element">
                                <span class="slds-form-element__label" style="color: black;">
                                    <template if:false={isCamper}>
                                        <abbr class="slds-required" title="required">* </abbr>
                                    </template>
                                    Uploaded Files
                                </span>
                                <div class="slds-form-element__control">
                                    <!-- <template for:each={data.uploadedFiles} for:item="files">
                                    <lightning-pill data-id={data.id} class="slds-p-bottom_small slds-p-right_small"
                                                    key={files.fileID} name={files.fileID} label={files.fileName}
                                                    onremove={removeFile}>
                                    </lightning-pill>
                                </template> -->
                                    <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                        onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                </div>
                            </div>
                        </template>
                    </lightning-layout-item>
                    <template lwc:if={PIReplacementItem}>
                        <b>IN DEVELOPMENT! WARRENTY ORDER SCREEN WILL BE HERE WITH SAME SKU ONLY</b>
                    </template>
                    <template lwc:elseif={PIStoreCredit}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-notify slds-notify_alert slds-alert_warning" role="alert">
                                <span class="slds-assistive-text">warning</span>
                                <span class="slds-icon_container slds-icon-utility-warning slds-m-right_x-small"
                                    title="Description of icon when needed">
                                    <svg class="slds-icon slds-icon_x-small" aria-hidden="true">
                                        <use xlink:href="/assets/icons/utility-sprite/svg/symbols.svg#warning"></use>
                                    </svg>
                                </span>
                                <h2>Noted!, Selection process for this claim proudct has been completed and you'll get
                                    Store
                                    credit once issue is approved.</h2>
                            </div>

                        </lightning-layout-item>
                    </template>
                    <template lwc:elseif={PIMoneyBack}>
                        <template lwc:if={isStoreChannelSale}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <c-claim-form-refund-form cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                    ondatachange={handleDataChange} approval-screen={approvalScreen}>
                                </c-claim-form-refund-form>
                            </lightning-layout-item>
                        </template>
                        <template lwc:else>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-notify slds-notify_alert slds-alert_warning" role="alert">
                                    <span class="slds-assistive-text">warning</span>
                                    <span class="slds-icon_container slds-icon-utility-warning slds-m-right_x-small"
                                        title="Description of icon when needed">
                                        <svg class="slds-icon slds-icon_x-small" aria-hidden="true">
                                            <use xlink:href="/assets/icons/utility-sprite/svg/symbols.svg#warning">
                                            </use>
                                        </svg>
                                    </span>
                                    <h2>Noted!, Selection process for this claim proudct has been completed and you'll
                                        get
                                        Store Refund once issue is approved.</h2>
                                </div>
                            </lightning-layout-item>
                        </template>
                    </template>
                </template>

                <template lwc:else>

                    <lightning-layout multiple-rows vertical-align="stretch">


                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_medium" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Sub Category
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.selectedIssueSubCategory}
                            </div>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_medium" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>

                            <lightning-combobox data-id={data.id} data-fieldlabel="selectedIssueSubCategory"
                                data-type="combobox" name="selectedIssueSubCategory" label="Select Sub Category"
                                value={data.selectedIssueSubCategory} placeholder="Select Sub Category"
                                options={productIssueProductNonRecallSubCategoryOptions} onchange={handleChange}
                                required read-only={approvalScreen}>
                            </lightning-combobox>

                        </lightning-layout-item>


                        <template lwc:if={PIDefectiveFaultyProduct}>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        <abbr class="slds-required" title="required">* </abbr>
                                        Did the issue cause smoke, fire, electrocution, injury, property damage, heat
                                        damage or melting?
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.didCauseDamage}
                                </div>
                            </lightning-layout-item>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="didCauseDamage"
                                    data-type="combobox" name="didCauseDamage"
                                    label="Did the issue cause smoke, fire, electrocution, injury, property damage, heat damage or melting?"
                                    value={data.didCauseDamage} placeholder="Please Select" options={checkboxOptions}
                                    onchange={handleChange} required read-only={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>

                            <template if:true={didCauseDamage}>
                                <template if:false={approvalScreen}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="3" flexibility="auto" multiple-rows>
                                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                            has-custom-field={trueVal} custom-field-name="File_category_fileupload__c"
                                            custom-field-value="Photo of Damage"
                                            input-label="Any Supporting Documentation / and or reports?">
                                        </c-claim-form-file-upload>
                                    </lightning-layout-item>
                                </template>
                            </template>

                            <template if:true={didCauseDamage}>
                                <template if:false={approvalScreen}>
                                    <lightning-layout-item
                                        class="slds-p-right_small slds-p-top_medium slds-p-bottom_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <b style="color:#ca1515">
                                            <p>
                                                DO NOT TEST this product.This claim will be escalated, please await for
                                                further instructions.
                                            </p>
                                        </b>
                                    </lightning-layout-item>
                                </template>
                            </template>

                            <template lwc:if={causeDamageValueSelected}>
                                <template if:true={didNotCauseDamage}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows
                                        if:false={approvalScreen}>
                                        <lightning-accordion active-section-name={accordianOpenSection}
                                            onsectiontoggle={handleSectionToggle} allow-multiple-sections-open>
                                            <lightning-accordion-section name="TroubleshootingFAQ"
                                                label="Troubleshooting FAQ" if:true={hasFaqData}>
                                                <lightning-formatted-rich-text value={faqData}>
                                                </lightning-formatted-rich-text>
                                            </lightning-accordion-section>
                                        </lightning-accordion>
                                        <template lwc:if={hasProductKnowladgeData}>
                                            <lightning-accordion active-section-name={accordianOpenSection}
                                                onsectiontoggle={handleSectionToggle} allow-multiple-sections-open>
                                                <template for:each={productKnowladgeData}
                                                    for:item="productKnowladgeDataObj" for:index="index">
                                                    <lightning-accordion-section name={productKnowladgeDataObj.Id}
                                                        label={productKnowladgeDataObj.Knowledge__r.Summary}
                                                        key={productKnowladgeDataObj.Id}>
                                                        <lightning-formatted-rich-text
                                                            value={productKnowladgeDataObj.Knowledge__r.Details__c}>
                                                        </lightning-formatted-rich-text>
                                                    </lightning-accordion-section>
                                                </template>
                                            </lightning-accordion>
                                        </template>
                                    </lightning-layout-item>
                                </template>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Issue Resolved?
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isIssueResolved}
                                    </div>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="isIssueResolved"
                                        data-type="combobox" name="isIssueResolved" label="Issue Resolved?"
                                        value={data.isIssueResolved} placeholder="Issue Resolved?"
                                        options={checkboxOptions} onchange={handleChange} required
                                        read-only={data.isIssueResolvedDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>

                                <template lwc:if={isIssueResolved}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <div class="slds-box slds-align_absolute-center">
                                            <h2><b>Confirmation of Resolution: Your issue has been successfully resolved.</b></h2>
                                        </div>
                                    </lightning-layout-item>
                                </template>

                                <template lwc:elseif={isIssueNotResolved}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                    </lightning-layout-item>

                                    <template if:true={didNotCauseDamage}>
                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                            large-device-size="3" flexibility="auto" multiple-rows
                                            if:true={approvalScreen}>
                                            <div class="slds-form-element">
                                                <label class="slds-form-element__label slds-text-title_bold">
                                                    Will a spare part fix the problem for this issue?
                                                </label>
                                            </div>
                                            <div class="slds-form-element__control">
                                                {data.willSparePartCanFixIssue}
                                            </div>
                                        </lightning-layout-item>

                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                            large-device-size="3" flexibility="auto" multiple-rows
                                            if:false={approvalScreen}>
                                            <lightning-combobox data-id={data.id}
                                                data-fieldlabel="willSparePartCanFixIssue" data-type="combobox"
                                                name="willSparePartCanFixIssue"
                                                label="Will a spare part fix the problem for this issue?"
                                                value={data.willSparePartCanFixIssue}
                                                placeholder="Will a spare part fix the problem for this issue?"
                                                options={checkboxOptions} onchange={handleChange} required
                                                read-only={approvalScreen}>
                                            </lightning-combobox>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto"
                                            multiple-rows if:false={hideSparePartTable}>
                                            <c-claim-form-c-p-item-product-selection cp-obj={data}
                                                full-card-data={cardData} erp-data={erpData}
                                                preselectedrows={data.selectProds} show-edit-qty-button={falseVal}
                                                ondatachange={handleDataChange} approval-screen={approvalScreen}
                                                is-showing-parts-for-issue-fix={trueVal} show-only-table={trueVal}
                                                showreadonly={trueVal}>
                                            </c-claim-form-c-p-item-product-selection>
                                        </lightning-layout-item>

                                    </template>

                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                    </lightning-layout-item>

                                    <template if:true={sparePartCanFix}>
                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                            large-device-size="3" flexibility="auto" multiple-rows
                                            if:true={approvalScreen}>
                                            <div class="slds-form-element">
                                                <label class="slds-form-element__label slds-text-title_bold">
                                                    Is the spare part needed present in the above list?
                                                </label>
                                            </div>
                                            <div class="slds-form-element__control">
                                                {data.isSpareAvailableInTable}
                                            </div>
                                        </lightning-layout-item>

                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                            large-device-size="3" flexibility="auto" multiple-rows
                                            if:false={approvalScreen}>
                                            <lightning-combobox data-id={data.id}
                                                data-fieldlabel="isSpareAvailableInTable" data-type="combobox"
                                                name="isSpareAvailableInTable"
                                                label="Is the spare part needed present in the above list?"
                                                value={data.isSpareAvailableInTable}
                                                placeholder="Is the spare part needed present in the above list?"
                                                options={checkboxOptions} onchange={handleChange} required
                                                read-only={approvalScreen}>
                                            </lightning-combobox>
                                        </lightning-layout-item>
                                    </template>

                                    <template if:true={isSpareNotAvailableInTable}>
                                        <lightning-layout-item
                                            class="slds-p-right_small slds-p-top_small slds-p-bottom_medium" size="12"
                                            large-device-size="12" flexibility="auto" multiple-rows
                                            if:true={isSpareNotAvailableInTable}>
                                            <b>Please lodge a ticket with the product team, to ensure this is solved for
                                                future
                                                customers</b> </br>
                                            <a href="https://emgcloud.atlassian.net/servicedesk/customer/portal/12/group/39/create/138"
                                                target="_blank">Click Here to Lodge a ticket </a>
                                        </lightning-layout-item>
                                    </template>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows
                                        if:true={showIssueNotResolvedOptions}>
                                        <c-claim-form-cp-item-product-issue-child cp-obj={data}
                                            full-card-data={cardData} index={index} approval-screen={approvalScreen}
                                            erp-data={erpData} serial-batch-url={urlName} onhandlechange={handleChange}
                                            onlinkfilestocard={linkFilesToCard} ondatachange={handleDataChange}
                                            onremovefile={removeFileForProductIssue}
                                            current-product-category={currentProductCategoryName}
                                            sf-case-i-d={sfCaseID} sf-claim-product-i-d={sfClaimProductID}
                                            sf-claim-product-action-i-d={sfClaimProductActionID}
                                            show-edit-qty-button={approvalScreen}>
                                        </c-claim-form-cp-item-product-issue-child>
                                    </lightning-layout-item>
                                </template>

                            </template>
                        </template>

                        <template lwc:elseif={PIPartMissing}>
                            <template if:false={approvalScreen}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows>
                                    <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                        uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                        required={trueVal} has-custom-field={trueVal}
                                        custom-field-name="File_category_fileupload__c"
                                        custom-field-value="Photo of Missing Part" input-label="Photo of Missing Part">
                                    </c-claim-form-file-upload>
                                </lightning-layout-item>
                            </template>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <template if:true={isFilesUploaded}>
                                    <div class="slds-form-element">
                                        <span class="slds-form-element__label" style="color: black;">
                                            <template if:false={isCamper}>
                                                <abbr class="slds-required" title="required">* </abbr>
                                            </template>
                                            Uploaded Files 
                                        </span>
                                        <div class="slds-form-element__control">
                                            <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                                onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                        </div>
                                    </div>
                                </template>
                            </lightning-layout-item>
                            
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="6" flexibility="auto" multiple-rows>
                                <lightning-layout multiple-rows vertical-align="stretch">
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                                        flexibility="auto" multiple-rows>
                                        <!-- <template if:true={data.serialVisible}> -->
                                            <div class="slds-form-element">
                                                <label class="slds-form-element__label">
                                                    <template if:true={isSerialRequired}>
                                                        <abbr class="slds-required" title="required">* </abbr>
                                                    </template>
                                                    Serial Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                        alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                                                    <template if:true={showPreviewSerialPhoto}> |
                                                        <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                                            variant="bare" alternative-text="View Serial Number Photo Location"
                                                            onclick={handleOpenSerialPhotoLocationModel}></lightning-button-icon> View
                                                        Serial Number Location
                                                    </template>
                                                </label>
                                            </div>
                                            <!-- <template if:true={data.serialVisible}> -->
                                                <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                                    label="Serial Number" value={data.serialValue} variant="label-hidden"
                                                    onchange={handleChange} read-only={approvalScreen} required={isSerialRequired}>
                                                </lightning-input>
                                                <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                                label="Serial Number" value={data.serialValue} variant="label-hidden"
                                                onchange={handleChange} required={data.serialRequired} read-only={approvalScreen}>
                                            </lightning-input> -->
                                            <!-- </template> -->
                                            <!-- <template if:false={data.serialRequired}>
                                                <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                                    label="Serial Number" value={data.serialValue} variant="label-hidden"
                                                    onchange={handleChange} required={data.serialRequired} read-only>
                                                </lightning-input>
                                            </template> -->
                    
                                        <!-- </template> -->
                                        <!-- <template if:false={data.serialVisible}>
                                            <div class="slds-form-element">
                                                <label class="slds-form-element__label">
                                                    {data.serialLabel} <lightning-button-icon icon-name="utility:info" variant="bare"
                                                        alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                                                </label>
                                                <div class="slds-form-element__control">
                                                    N/A
                                                </div>
                                            </div>
                                        </template> -->
                                    </lightning-layout-item>

                                    <template if:false={approvalScreen}>                                        
                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                            large-device-size="6" flexibility="auto" multiple-rows>
                                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                                uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                                has-custom-field={trueVal} required={isSerialRequired}
                                                custom-field-name="File_category_fileupload__c" custom-field-value="Serial Location Photo"
                                                input-label="Photo/Video of Serial Location">
                                            </c-claim-form-file-upload>
                                        </lightning-layout-item>
                                    </template>

                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                                    flexibility="auto" multiple-rows>
                                    <!-- <template if:true={data.batchVisible}> -->
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label">
                                                <template if:true={isBatchRequired}>
                                                    <abbr class="slds-required" title="required">* </abbr>
                                                </template>
                                                Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon> <span style="color:red" if:false={approvalScreen}>(A batch number is required for all product faults)</span>
                                                <template if:true={showPreviewBatchPhoto}> |
                                                    <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                                        variant="bare" alternative-text="View Batch Photo Location"
                                                        onclick={handleOpenBatchPhotoLocationModel}></lightning-button-icon> View Batch
                                                    Photo Location
                                                </template>
                                            </label>
                                        </div>
                                        <!-- <template if:true={isBatchRequired}> -->
                                            <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                                label="Batch Number" value={data.batchValue} variant="label-hidden"
                                                onchange={handleChange} required={data.batchRequired} read-only={approvalScreen}>
                                            </lightning-input> -->
                                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                            label="Batch Number" value={data.batchValue} variant="label-hidden"
                                            onchange={handleChange} read-only={approvalScreen} required={isBatchRequired}>
                                        </lightning-input>
                                        <!-- </template> -->
                                        <!-- <template if:false={isBatchRequired}>
                                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                                label="Batch Number" value={data.batchValue} variant="label-hidden"
                                                onchange={handleChange} required={isBatchRequired} read-only>
                                            </lightning-input>
                                        </template> -->
                
                                    <!-- </template> -->
                                    <!-- <template if:false={data.batchVisible}>
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label">
                                                Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon> <span style="color:red" if:false={approvalScreen}>(A batch number is required for all product faults)</span>
                                            </label>
                                            <div class="slds-form-element__control">
                                                N/A
                                            </div>
                                        </div>
                                    </template> -->
                                </lightning-layout-item>
                

                                    
                    
                                  
                                    <template if:false={approvalScreen}>
                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                            large-device-size="6" flexibility="auto" multiple-rows>
                                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                                uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                                has-custom-field={trueVal} required={isBatchRequired}
                                                custom-field-name="File_category_fileupload__c" custom-field-value="Batch Location Photo"
                                                input-label="Photo/Video of Batch Location">
                                            </c-claim-form-file-upload>
                                        </lightning-layout-item>
                                    </template>
                                </lightning-layout>
                            </lightning-layout-item>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            </lightning-layout-item>
                            
                            <template if:true={sparePartCanFixForPartMissing}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows
                                    if:true={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Is the missing part needed present in the below list?
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isSpareAvailableInTable}
                                    </div>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows
                                    if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id}
                                        data-fieldlabel="isSpareAvailableInTable" data-type="combobox"
                                        name="isSpareAvailableInTable"
                                        label="Is the missing part needed present in the below list?"
                                        value={data.isSpareAvailableInTable}
                                        placeholder="Is the spare part needed present in the above list?"
                                        options={checkboxOptions} onchange={handleChange} required
                                        read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                            </template>

                            <template if:true={isSpareNotAvailableInTable}>
                                <lightning-layout-item
                                    class="slds-p-right_small slds-p-top_small slds-p-bottom_medium" size="12"
                                    large-device-size="12" flexibility="auto" multiple-rows
                                    if:true={isSpareNotAvailableInTable}>
                                    <b>Please lodge a ticket with the product team, to ensure this is solved for
                                        future
                                        customers</b> </br>
                                    <a href="https://emgcloud.atlassian.net/servicedesk/customer/portal/12/group/39/create/138"
                                        target="_blank">Click Here to Lodge a ticket </a>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Customer Outcome
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.resolutionOutcome}
                                </div>
                                </lightning-layout-item>

                                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                        data-type="combobox" name="resolutionOutcome" label="Select Customer Outcome"
                                        value={data.resolutionOutcome} placeholder="Customer Outcome"
                                        options={rtsOutcomeOptions} onchange={handleChange} required
                                        read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Can the item be immediately added back to stock now?
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.addBackToStockAction}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={showReadOnly}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="addBackToStockAction"
                                    data-type="combobox" name="addBackToStockAction"
                                    label="Can the item be immediately added back to stock now?"
                                    value={data.addBackToStockAction}
                                    placeholder="Can the item be immediately added back to stock now?"
                                    options={addBackToStockOptions} onchange={handleChange} required
                                    read-only={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>
                            <template lwc:if={addBackToStockActionTypeNow}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Add back to stock Location
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.addBackToStockActionLocation}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={showReadOnly}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="userLocation"
                                        data-type="combobox" name="userLocation" label="Add back to stock Location"
                                        value={data.userLocation} placeholder="Add back to stock Location"
                                        options={addBackToStockLocationOptions} onchange={handleChange} required
                                        read-only={isAddBackToStockLocationChangeDisabled}
                                        disabled={isAddBackToStockLocationChangeDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={isBrandNewConditionDisabled}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Is product brand new in box condition
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isBrandNewCondition}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={isBrandNewConditionDisabled}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="isBrandNewCondition"
                                        data-type="combobox" name="isBrandNewCondition"
                                        label="Is product brand new in box condition?" value={data.isBrandNewCondition}
                                        placeholder="Is product brand new in box condition" options={checkboxOptions}
                                        onchange={handleChange} required read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>


                                <template lwc:if={isBrandNewConditionIsYes}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock. Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                                <template lwc:if={isBrandNewConditionIsNo}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock(Seconds). Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                            </template>
                            <template lwc:if={isReturnRequired}>
                                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                    <c-claimform-return-order cp-obj={data} full-card-data={cardData} ondatachange={handleDataChange} erp-data={erpData}
                                        approval-screen={approvalScreen}></c-claimform-return-order>
                                </lightning-layout-item>
                            </template>
                            </template>

                            <template lwc:if={showProductIssueChildCompFromPartMissing}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="12" flexibility="auto" multiple-rows>
                                    <c-claim-form-cp-item-product-issue-child cp-obj={data} full-card-data={cardData}
                                        index={index} approval-screen={approvalScreen} erp-data={erpData}
                                        serial-batch-url={urlName} 
                                        sf-case-i-d={sfCaseID} sf-claim-product-i-d={sfClaimProductID}
                                        sf-claim-product-action-i-d={sfClaimProductActionID}
                                        onhandlechange={handleChange}
                                        onlinkfilestocard={linkFilesToCard} ondatachange={handleDataChange}
                                        onremovefile={removeFile} show-edit-qty-button={approvalScreen}>
                                    </c-claim-form-cp-item-product-issue-child>
                                </lightning-layout-item>
                            </template>
                            <template lwc:if={isNoPartsRequired}>
                            <lightning-layout-item size="12" flexibility="auto" multiple-rows>
                                <c-claim-form-c-p-item-product-selection cp-obj={data} full-card-data={cardData}
                                    erp-data={erpData} preselectedrows={data.selectProds}
                                    onproductselectionchange={handleProductSelectionChange}
                                    ondatachange={handleDataChange}
                                    approval-screen={approvalScreen}></c-claim-form-c-p-item-product-selection>
    
                            </lightning-layout-item>
                           </template>

                        </template>
                       

                    </lightning-layout>
                </template>
            </template>

            <!-- Transit/Delivery Issue Category -->
            <template lwc:elseif={isIssueCategoryIsTransitAndDeliveryIssue}>
                <lightning-layout multiple-rows vertical-align="stretch">

                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                Selected Issue with Transit/Delivery
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.selectedIssueSubCategory}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:false={showReadOnly}>
                        <lightning-combobox data-id={data.id} data-fieldlabel="selectedIssueSubCategory"
                            data-type="combobox" name="selectedIssueSubCategory"
                            label="Please Select the issue with transit/delivery" value={data.selectedIssueSubCategory}
                            placeholder="Please Select what issue you have with transit/delivery"
                            options={transitAndDeliveryIssueSubCategoryOptions} onchange={handleChange} required
                            read-only={approvalScreen}>
                        </lightning-combobox>
                        <template if:true={TDIReturnedtoSender}>
                            <div style="color: #0781d9 ;">
                                Please note that the customer will be notified via email and SMS.
                            </div>
                        </template>
                    </lightning-layout-item>

                    <template lwc:if={TDIDispatchIssue}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Select Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={dispatchLateOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>
                        <template lwc:if={is72HoursBefore}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-notify slds-notify_alert slds-alert_warning" role="alert">
                                    <span class="slds-assistive-text">warning</span>
                                    <span class="slds-icon_container slds-icon-utility-warning slds-m-right_x-small"
                                        title="Description of icon when needed">
                                        <svg class="slds-icon slds-icon_x-small" aria-hidden="true">
                                            <use xlink:href="/assets/icons/utility-sprite/svg/symbols.svg#warning">
                                            </use>
                                        </svg>
                                    </span>
                                    <h2>We Usually dispatch the item within 48hours of order
                                    </h2>
                                </div>
                            </lightning-layout-item>
                        </template>

                    </template>
                    <template lwc:elseif={TDIReturnedtoSender}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Why has the product been returned?
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.whyHasTheProductBeenReturned}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={showReadOnly}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="whyHasTheProductBeenReturned"
                                data-type="combobox" name="whyHasTheProductBeenReturned"
                                label="Why has the product been returned?" value={data.whyHasTheProductBeenReturned}
                                placeholder="Why has the product been returned?" options={returnOptions}
                                onchange={handleChange} required read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>
                        <template if:true={isReturnReasonIsOther}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Other
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.otherReturnReason}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:false={showReadOnly}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label">
                                        <abbr class="slds-required" title="required">* </abbr>
                                        Other
                                    </label>
                                </div>
                                <lightning-input data-id={data.id} data-type="input" data-fieldlabel="otherReturnReason"
                                    label="Other" value={data.otherReturnReason} variant="label-hidden"
                                    onchange={handleChange} required type="text" read-only={approvalScreen}>
                                </lightning-input>
                            </lightning-layout-item>
                        </template>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-align_absolute-center"
                            size="12" large-device-size="3" flexibility="auto" multiple-rows if:false={showReadOnly}>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                has-custom-field={trueVal} custom-field-name="File_category_fileupload__c"
                                custom-field-value="Photo RTS Proof"
                                input-label="Please upload a photo of the returned item including the courier label"
                                required={trueVal}>
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-align_absolute-center"
                            size="12" large-device-size="3" flexibility="auto" multiple-rows
                            if:false={isReturnReasonIsOther}>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-form-element">
                                <span class="slds-form-element__label" style="color: black;">
                                    <template if:false={isCamper}>
                                        <abbr class="slds-required" title="required">* </abbr>
                                    </template>
                                    Uploaded Files
                                </span>
                                <div class="slds-form-element__control">
                                    <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                        onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                </div>
                            </div>
                        </lightning-layout-item>
                        <template lwc:if={data.isShipped}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Can the item be immediately added back to stock now?
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.addBackToStockAction}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={showReadOnly}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="addBackToStockAction"
                                    data-type="combobox" name="addBackToStockAction"
                                    label="Can the item be immediately added back to stock now?"
                                    value={data.addBackToStockAction}
                                    placeholder="Can the item be immediately added back to stock now?"
                                    options={addBackToStockOptions} onchange={handleChange} required
                                    read-only={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>
                            <template lwc:if={addBackToStockActionTypeNow}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Add back to stock Location
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.addBackToStockActionLocation}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={showReadOnly}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="userLocation"
                                        data-type="combobox" name="userLocation" label="Add back to stock Location"
                                        value={data.userLocation} placeholder="Add back to stock Location"
                                        options={addBackToStockLocationOptions} onchange={handleChange} required
                                        read-only={isAddBackToStockLocationChangeDisabled}
                                        disabled={isAddBackToStockLocationChangeDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={isBrandNewConditionDisabled}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Is product brand new in box condition
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isBrandNewCondition}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={isBrandNewConditionDisabled}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="isBrandNewCondition"
                                        data-type="combobox" name="isBrandNewCondition"
                                        label="Is product brand new in box condition?" value={data.isBrandNewCondition}
                                        placeholder="Is product brand new in box condition" options={checkboxOptions}
                                        onchange={handleChange} required read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>


                                <template lwc:if={isBrandNewConditionIsYes}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock. Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                                <template lwc:if={isBrandNewConditionIsNo}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock(Seconds). Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                            </template>
                        </template>
                        <!-- <template lwc:if={sfClaimProductActionID}> -->
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Customer Outcome
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.resolutionOutcome}
                                </div>
                            </lightning-layout-item>

                            <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            </lightning-layout-item>

                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                    data-type="combobox" name="resolutionOutcome" label="Select Customer Outcome"
                                    value={data.resolutionOutcome} placeholder="Customer Outcome"
                                    options={rtsOutcomeOptions} onchange={handleChange} required
                                    read-only={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>
                        <!-- </template> -->
                    </template>
                    <template lwc:elseif={TDIDamagedinTransit}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Damage Description
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.damageDescription}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label">
                                    <abbr class="slds-required" title="required">* </abbr>
                                    Damage Description
                                </label>
                            </div>
                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel="damageDescription"
                                label="Damage Description" value={data.damageDescription} variant="label-hidden"
                                onchange={handleChange} required type="text" read-only={approvalScreen}>
                            </lightning-input>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-align_absolute-center"
                            size="12" large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                has-custom-field={trueVal} custom-field-name="File_category_fileupload__c"
                                custom-field-value="Photo of Damage" input-label="Photo of Damage" required={trueVal}>
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={wrongProductOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="6" flexibility="auto" multiple-rows>
                            <lightning-layout multiple-rows vertical-align="stretch">
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                                    flexibility="auto" multiple-rows>
                                    <!-- <template if:true={data.serialVisible}> -->
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label">
                                                <template if:true={isSerialRequired}>
                                                    <abbr class="slds-required" title="required">* </abbr>
                                                </template>
                                                Serial Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                                                <template if:true={showPreviewSerialPhoto}> |
                                                    <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                                        variant="bare" alternative-text="View Serial Number Photo Location"
                                                        onclick={handleOpenSerialPhotoLocationModel}></lightning-button-icon> View
                                                    Serial Number Location
                                                </template>
                                            </label>
                                        </div>
                                        <!-- <template if:true={data.serialVisible}> -->
                                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                                label="Serial Number" value={data.serialValue} variant="label-hidden"
                                                onchange={handleChange} read-only={approvalScreen} required={isSerialRequired}>
                                            </lightning-input>
                                            <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                            label="Serial Number" value={data.serialValue} variant="label-hidden"
                                            onchange={handleChange} required={data.serialRequired} read-only={approvalScreen}>
                                        </lightning-input> -->
                                        <!-- </template> -->
                                        <!-- <template if:false={data.serialRequired}>
                                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                                label="Serial Number" value={data.serialValue} variant="label-hidden"
                                                onchange={handleChange} required={data.serialRequired} read-only>
                                            </lightning-input>
                                        </template> -->
                
                                    <!-- </template> -->
                                    <!-- <template if:false={data.serialVisible}>
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label">
                                                {data.serialLabel} <lightning-button-icon icon-name="utility:info" variant="bare"
                                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                                            </label>
                                            <div class="slds-form-element__control">
                                                N/A
                                            </div>
                                        </div>
                                    </template> -->
                                </lightning-layout-item>

                                <template if:false={approvalScreen}>                                        
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                        large-device-size="6" flexibility="auto" multiple-rows>
                                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                            has-custom-field={trueVal} required={isSerialRequired}
                                            custom-field-name="File_category_fileupload__c" custom-field-value="Serial Location Photo"
                                            input-label="Photo/Video of Serial Location">
                                        </c-claim-form-file-upload>
                                    </lightning-layout-item>
                                </template>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                                flexibility="auto" multiple-rows>
                                <!-- <template if:true={data.batchVisible}> -->
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            <template if:true={isBatchRequired}>
                                                <abbr class="slds-required" title="required">* </abbr>
                                            </template>
                                            Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                alternative-text="Info" onclick={navigateURL}></lightning-button-icon> <span style="color:red" if:false={approvalScreen}>(A batch number is required for all product faults)</span>
                                            <template if:true={showPreviewBatchPhoto}> |
                                                <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                                    variant="bare" alternative-text="View Batch Photo Location"
                                                    onclick={handleOpenBatchPhotoLocationModel}></lightning-button-icon> View Batch
                                                Photo Location
                                            </template>
                                        </label>
                                    </div>
                                    <!-- <template if:true={isBatchRequired}> -->
                                        <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                            label="Batch Number" value={data.batchValue} variant="label-hidden"
                                            onchange={handleChange} required={data.batchRequired} read-only={approvalScreen}>
                                        </lightning-input> -->
                                        <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                        label="Batch Number" value={data.batchValue} variant="label-hidden"
                                        onchange={handleChange} read-only={approvalScreen} required={isBatchRequired}>
                                    </lightning-input>
                                    <!-- </template> -->
                                    <!-- <template if:false={isBatchRequired}>
                                        <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                                            label="Batch Number" value={data.batchValue} variant="label-hidden"
                                            onchange={handleChange} required={isBatchRequired} read-only>
                                        </lightning-input>
                                    </template> -->
            
                                <!-- </template>
                                <template if:false={data.batchVisible}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                                alternative-text="Info" onclick={navigateURL}></lightning-button-icon> <span style="color:red" if:false={approvalScreen}>(A batch number is required for all product faults)</span>
                                        </label>
                                        <div class="slds-form-element__control">
                                            N/A
                                        </div>
                                    </div>
                                </template> -->
                            </lightning-layout-item>
            

                                
                
                                
                                <template if:false={approvalScreen}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6"
                                        large-device-size="6" flexibility="auto" multiple-rows>
                                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                            has-custom-field={trueVal} required={isBatchRequired}
                                            custom-field-name="File_category_fileupload__c" custom-field-value="Batch Location Photo"
                                            input-label="Photo/Video of Batch Location">
                                        </c-claim-form-file-upload>
                                    </lightning-layout-item>
                                </template>
                            </lightning-layout>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="12" flexibility="auto" multiple-rows>
                        </lightning-layout-item>
                        
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-form-element">
                                <span class="slds-form-element__label" style="color: black;">
                                    <template if:false={isCamper}>
                                    </template>
                                    Uploaded Files
                                </span>
                                <div class="slds-form-element__control">
                                    <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                        onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                </div>
                            </div>
                        </lightning-layout-item>
                        
                        <template lwc:if={data.isShipped}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Can the item be immediately added back to stock now?
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.addBackToStockAction}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={showReadOnly}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="addBackToStockAction"
                                    data-type="combobox" name="isBrandNewCondition"
                                    label="Can the item be immediately added back to stock now?"
                                    value={data.addBackToStockAction}
                                    placeholder="Can the item be immediately added back to stock now?"
                                    options={addBackToStockOptions} onchange={handleChange} 
                                    read-only={data.addBackToStockActionDisabled}>
                                </lightning-combobox>
                            </lightning-layout-item>
                            <template lwc:if={addBackToStockActionTypeNow}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:true={showReadOnly}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Add back to stock Location (Seconds)
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.addBackToStockActionLocation}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={showReadOnly}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="userLocation"
                                        data-type="combobox" name="userLocation" label="Add back to stock Location"
                                        value={data.userLocation} placeholder="Add back to stock Location"
                                        options={addBackToStockLocationOptions} onchange={handleChange} required
                                        read-only={isAddBackToStockLocationChangeDisabled}
                                        disabled={isAddBackToStockLocationChangeDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:true={isBrandNewConditionDisabled}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Is product brand new in box condition
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isBrandNewCondition}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-top_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={isBrandNewConditionDisabled}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="isBrandNewCondition" data-type="combobox"
                                        name="isBrandNewCondition" label="Is product brand new in box condition?"
                                        value={data.isBrandNewCondition} placeholder="Is product brand new in box condition"
                                        options={checkboxOptions} onchange={handleChange} required read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                                <template lwc:if={isBrandNewConditionIsYes}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium slds-p-bottom_medium" size="12" large-device-size="12"
                                        flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock. Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                                <template lwc:if={isBrandNewConditionIsNo}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium slds-p-bottom_medium" size="12" large-device-size="12"
                                        flexibility="auto" multiple-rows>
                                        <span><b>The item will be Added Back to Stock(Seconds). Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                </lightning-layout-item>
                            </template>
                        </template>
                    </template>
                    <template lwc:elseif={TDILostinTransit}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Lost in Transit Description
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.lostinTransitDescription}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label">
                                    <abbr class="slds-required" title="required">* </abbr>
                                    Lost in Transit Description
                                </label>
                            </div>
                            <lightning-input data-id={data.id} data-type="input"
                                data-fieldlabel="lostinTransitDescription" label="Lost in Transit Description"
                                value={data.lostinTransitDescription} variant="label-hidden" onchange={handleChange}
                                required type="text" read-only={approvalScreen}>
                            </lightning-input>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-align_absolute-center"
                            size="12" large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>

                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                has-custom-field={trueVal} custom-field-name="File_category_fileupload__c"
                                custom-field-value="Lost Transit Screenshot" input-label="Lost Transit Screenshot"
                                required={falseVal}>
                            </c-claim-form-file-upload>
                            <lightning-helptext class="slds-p-bottom_x-large"
                                content="Please upload photo of tracking link and proof that item's been lost in transit">
                            </lightning-helptext>

                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={wrongProductOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-form-element">
                                <span class="slds-form-element__label" style="color: black;">
                                    <template if:false={isCamper}>
                                        <abbr class="slds-required" title="required">* </abbr>
                                    </template>
                                    Uploaded Files
                                </span>
                                <div class="slds-form-element__control">
                                    <!-- <template for:each={data.uploadedFiles} for:item="files">
                                <lightning-pill data-id={data.id} class="slds-p-bottom_small slds-p-right_small"
                                                key={files.fileID} name={files.fileID} label={files.fileName}
                                                onremove={removeFile}>
                                </lightning-pill>
                            </template> -->
                                    <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                        onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                </div>
                            </div>
                        </lightning-layout-item>

                    </template>
                    <template lwc:elseif={TDIWrongProductDispatched}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    What is the SKU on the box of product you have received?
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.CP_Incorrecte_SKU__c}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label">
                                    <abbr class="slds-required" title="required">* </abbr>
                                    What is the SKU on the box of product you have received?
                                </label>
                            </div>
                            <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel="CP_Incorrecte_SKU__c" label="What is the SKU on the box of product you have received?"
                                value={data.CP_Incorrecte_SKU__c} variant="label-hidden"
                                onchange={handleChange} required type="text" read-only={approvalScreen}>
                    </lightning-input> -->
                            <c-reusuable-lookup disabled={approvalScreen} required={trueVal}
                                onvalueselected={handleSKUSelection} onvalueremoved={handleSKURemoval}
                                show-icon-on-left={trueVal} show-secondary-fields={falseVal}
                                varient-value='label-hidden' object-api-name="Product2" field-api-name="SKU__c"
                                other-field-api-name="Name" require-selection={trueVal} make-text-upper-case={trueVal}
                                selected-record-id={data.CP_Incorrecte_SKU_Id__c}
                                selected-record-name={data.CP_Incorrecte_SKU__c}
                                additional-filters={productSearchAdditionalFilter}></c-reusuable-lookup>
                        </lightning-layout-item>
                        <!-- <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3" flexibility="auto" multiple-rows  if:true={approvalScreen}>
                    <div class="slds-form-element">
                        <label class="slds-form-element__label slds-text-title_bold">
                            Incorrect Product Description
                        </label>
                    </div>
                    <div class="slds-form-element__control">
                        {data.CP_Incorrect_SKU_Description__c}
                    </div>
                </lightning-layout-item>
                <lightning-layout-item  class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3" flexibility="auto" multiple-rows padding="left-medium" if:false={approvalScreen}>
                        <lightning-textarea name="CP_Incorrect_SKU_Description__c" label="Incorrect Product Description"
                        data-id="CP_Incorrect_SKU_Description__c" data-type="input" data-fieldlabel="CP_Incorrect_SKU_Description__c"
                        onchange={handleChange}
                        value={data.CP_Incorrect_SKU_Description__c}></lightning-textarea>
                </lightning-layout-item> -->
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} has-custom-field={trueVal}
                                custom-field-name="File_category_fileupload__c" custom-field-value="Incorrect Proof"
                                onupdateddata={linkFilesToCard} input-label="Please upload a photo of incorrect product"
                                required={trueVal}>
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                        <template if:true={isFilesUploaded}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-form-element">
                                    <span class="slds-form-element__label" style="color: black;">
                                        <template if:false={isCamper}>
                                            <abbr class="slds-required" title="required">* </abbr>
                                        </template>
                                        Uploaded Files
                                    </span>
                                    <div class="slds-form-element__control">
                                        <!-- <template for:each={data.uploadedFiles} for:item="files">
                                        <lightning-pill data-id={data.id} class="slds-p-bottom_small slds-p-right_small"
                                                        key={files.fileID} name={files.fileID} label={files.fileName}
                                                        onremove={removeFile}>
                                        </lightning-pill>
                                    </template> -->
                                        <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                            onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                    </div>
                                </div>
                            </lightning-layout-item>
                        </template>
                        
                        <template lwc:if={data.isShipped}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Can the item be immediately added back to stock now?
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.addBackToStockAction}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="addBackToStockAction"
                                    data-type="combobox" name="isBrandNewCondition"
                                    label="Can the item be immediately added back to stock now?"
                                    value={data.addBackToStockAction}
                                    placeholder="Can the item be immediately added back to stock now?"
                                    options={addBackToStockOptions} onchange={handleChange} required
                                    read-only={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>
                            <template lwc:if={addBackToStockActionTypeNow}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Add back to stock Location
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.addBackToStockActionLocation}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="userLocation"
                                        data-type="combobox" name="userLocation" label="Add back to stock Location"
                                        value={data.userLocation} placeholder="Add back to stock Location"
                                        options={addBackToStockLocationOptions} onchange={handleChange} required
                                        read-only={isAddBackToStockLocationChangeDisabled}
                                        disabled={isAddBackToStockLocationChangeDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows if:true={isBrandNewConditionDisabled}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Is product brand new in box condition
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.isBrandNewCondition}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                    flexibility="auto" multiple-rows if:false={isBrandNewConditionDisabled}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="isBrandNewCondition"
                                        data-type="combobox" name="isBrandNewCondition"
                                        label="Is product brand new in box condition?" value={data.isBrandNewCondition}
                                        placeholder="Is product brand new in box condition" options={checkboxOptions}
                                        onchange={handleChange} required read-only={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>


                                <lightning-layout-item class="slds-p-right_small slds-p-top_medium slds-p-top_medium" size="12"
                                    large-device-size="12" flexibility="auto" multiple-rows>
                                    <span><b>We need to arrange the return of the incorrect product, and issue a replacement
                                            product
                                            or
                                            refund. </b></span>
                                </lightning-layout-item>

                                <template lwc:if={isBrandNewConditionIsYes}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                        large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be added back to stock. Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                                <template lwc:if={isBrandNewConditionIsNo}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-top_medium slds-p-bottom_small"
                                        size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                        <span><b>The item will be added back to stock(Seconds). Customer and Call centre will be
                                                notified</b></span>
                                    </lightning-layout-item>
                                </template>
                            </template>
                        </template>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small " size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={wrongProductOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>
                    </template>
                    <template lwc:elseif={TDIWrongProductQuantityDispatched}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                How many did you receive?
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.CP_Quantity_Dispatched__c}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                <abbr class="slds-required" title="required">* </abbr>
                                How many did you receive?
                            </label>
                        </div>
                        <lightning-input data-id={data.id} data-type="input"
                            data-fieldlabel="CP_Quantity_Dispatched__c" label="How many did you receive?"
                            value={data.CP_Quantity_Dispatched__c} variant="label-hidden" onchange={handleChange}
                            required type="number" read-only={approvalScreen} 
                            min="0" message-when-range-underflow="Received QTY is too low. Please enter 0 to continue"
                            max="0" message-when-range-overflow="Received QTY is too high. Please enter 0 to continue">
                        </lightning-input>
                    </lightning-layout-item>
                        <lightning-layout-item class="sl3ds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows padding="left-medium">
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold" if:true={approvalScreen}>
                                    Ordered Quantity
                                </label>
                                <label class="slds-form-element__label" if:false={approvalScreen}>
                                    Ordered Quantity
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                1
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows padding="left-medium">
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold" if:true={approvalScreen}>
                                    Quantity Difference
                                </label>
                                <label class="slds-form-element__label" if:false={approvalScreen}>
                                    Quantity Difference
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.CP_Quantity_Difference__c}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                uploaded-files={data.uploadedFiles} has-custom-field={trueVal}
                                custom-field-name="File_category_fileupload__c" custom-field-value="Incorrect Proof"
                                onupdateddata={linkFilesToCard}
                                input-label="Please upload a photo/screenshot of incorrect product quantity"
                                required={trueVal}>
                            </c-claim-form-file-upload>
                        </lightning-layout-item>
                        <template if:true={isFilesUploaded}>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="12"
                                flexibility="auto" multiple-rows>
                                <div class="slds-form-element">
                                    <span class="slds-form-element__label" style="color: black;">
                                        <template if:false={isCamper}>
                                            <abbr class="slds-required" title="required">* </abbr>
                                        </template>
                                        Uploaded Files
                                    </span>
                                    <div class="slds-form-element__control">

                                        <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                            onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                    </div>
                                </div>
                            </lightning-layout-item>
                        </template>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={incorrectQuantityDispatchOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>
                    </template>

                    <template lwc:if={isReturnRequired}>
                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <c-claimform-return-order cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                ondatachange={handleDataChange}
                                approval-screen={approvalScreen}></c-claimform-return-order>
                        </lightning-layout-item>
                    </template>

                    <template lwc:if={isOutcomeIsReplacementAny}>
                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <c-claim-form-c-p-item-product-selection cp-obj={data} full-card-data={cardData}
                                erp-data={erpData} preselectedrows={data.selectProds}
                                onproductselectionchange={handleProductSelectionChange} show-edit-qty-button={falseVal}
                                ondatachange={handleDataChange} approval-screen={approvalScreen}>
                            </c-claim-form-c-p-item-product-selection>
                        </lightning-layout-item>
                    </template>

                    <template lwc:if={isOutcomeIsMoneyBack}>
                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <c-claim-form-refund-form cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                ondatachange={handleDataChange}
                                approval-screen={approvalScreen}></c-claim-form-refund-form>
                        </lightning-layout-item>
                    </template>

                   
                </lightning-layout>
            </template>

            <!-- Order Issue Category -->
            <template lwc:elseif={isIssueCategoryIsOrderIssue}>
                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                    flexibility="auto" multiple-rows>
                    <c-claim-form-order-issue cp-obj={data} full-card-data={cardData} erp-data={erpData}
                        ondatachange={handleDataChange} approval-screen={approvalScreen}></c-claim-form-order-issue>
                </lightning-layout-item>
            </template>
        </lightning-layout-item>
    </lightning-layout>

    <lightning-layout multiple-rows vertical-align="stretch">
        <!-- Associated Product -->
        <template lwc:if={isIssueCategoryIsAssociatedProduct}>
            <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12" large-device-size="3"
                flexibility="auto" multiple-rows>
                <span><b>This Product is part of a faulty/damaged product that is being claimed</b></span>
            </lightning-layout-item>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-top_small" size="12"
                large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                <div class="slds-form-element">
                    <label class="slds-form-element__label slds-text-title_bold">
                        Customer Outcome
                    </label>
                </div>
                <div class="slds-form-element__control">
                    {data.resolutionOutcome}
                </div>
            </lightning-layout-item>
            <lightning-layout-item class="slds-p-right_small slds-p-top_small" size="12" large-device-size="3"
                flexibility="auto" multiple-rows if:false={approvalScreen}>
                <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome" data-type="combobox"
                    name="resolutionOutcome" label="Select Customer Outcome" value={data.resolutionOutcome}
                    placeholder="Customer Outcome" options={associatedProductOutcomeOptions} onchange={handleChange}
                    required read-only={approvalScreen}>
                </lightning-combobox>
            </lightning-layout-item>
            <template lwc:if={data.isShipped}>
                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                    flexibility="auto" multiple-rows if:true={approvalScreen}>
                    <div class="slds-form-element">
                        <label class="slds-form-element__label slds-text-title_bold">
                            Can the item be immediately added back to stock now?
                        </label>
                    </div>
                    <div class="slds-form-element__control">
                        {data.addBackToStockAction}
                    </div>
                </lightning-layout-item>
                <lightning-layout-item class="slds-p-right_small slds-p-top_small" size="12" large-device-size="3"
                    flexibility="auto" multiple-rows if:false={approvalScreen}>
                    <lightning-combobox data-id={data.id} data-fieldlabel="addBackToStockAction" data-type="combobox"
                        name="isBrandNewCondition" label="Can the item be immediately added back to stock now?"
                        value={data.addBackToStockAction} placeholder="Can the item be immediately added back to stock now?"
                        options={addBackToStockOptions} onchange={handleChange} required read-only={approvalScreen}>
                    </lightning-combobox>
                </lightning-layout-item>
                <template lwc:if={addBackToStockActionTypeNow}>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                        flexibility="auto" multiple-rows if:true={approvalScreen}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                Add back to stock Location
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.addBackToStockActionLocation}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-top_small" size="12" large-device-size="3"
                        flexibility="auto" multiple-rows if:false={approvalScreen}>
                        <lightning-combobox data-id={data.id} data-fieldlabel="userLocation" data-type="combobox"
                            name="userLocation" label="Add back to stock Location" value={data.userLocation}
                            placeholder="Add back to stock Location" options={addBackToStockLocationOptions}
                            onchange={handleChange} required read-only={isAddBackToStockLocationChangeDisabled}
                            disabled={isAddBackToStockLocationChangeDisabled}>
                        </lightning-combobox>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                        flexibility="auto" multiple-rows if:true={isBrandNewConditionDisabled}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                Is product brand new in box condition
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.isBrandNewCondition}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-top_small" size="12" large-device-size="3"
                        flexibility="auto" multiple-rows if:false={isBrandNewConditionDisabled}>
                        <lightning-combobox data-id={data.id} data-fieldlabel="isBrandNewCondition" data-type="combobox"
                            name="isBrandNewCondition" label="Is product brand new in box condition?"
                            value={data.isBrandNewCondition} placeholder="Is product brand new in box condition"
                            options={checkboxOptions} onchange={handleChange} required read-only={approvalScreen}>
                        </lightning-combobox>
                    </lightning-layout-item>
                    <template lwc:if={isBrandNewConditionIsYes}>
                        <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12" large-device-size="12"
                            flexibility="auto" multiple-rows>
                            <span><b>The item will be Added Back to Stock. Customer and Call centre will be
                                    notified</b></span>
                        </lightning-layout-item>
                    </template>
                    <template lwc:if={isBrandNewConditionIsNo}>
                        <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12" large-device-size="12"
                            flexibility="auto" multiple-rows>
                            <span><b>The item will be Added Back to Stock(Seconds). Customer and Call centre will be
                                    notified</b></span>
                        </lightning-layout-item>
                    </template>
                    <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                    </lightning-layout-item>
                </template>
            </template>

            <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12" large-device-size="3"
                flexibility="auto" multiple-rows>
            </lightning-layout-item>

            <template lwc:if={isReturnRequired}>
                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                    <c-claimform-return-order cp-obj={data} full-card-data={cardData} ondatachange={handleDataChange} erp-data={erpData}
                        approval-screen={approvalScreen}></c-claimform-return-order>
                </lightning-layout-item>
            </template>

            <template lwc:if={isOutcomeIsReplacementAny}>
                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                    <c-claim-form-c-p-item-product-selection cp-obj={data} full-card-data={cardData} erp-data={erpData}
                        preselectedrows={data.selectProds} onproductselectionchange={handleProductSelectionChange}
                        show-edit-qty-button={falseVal} ondatachange={handleDataChange}
                        approval-screen={approvalScreen}></c-claim-form-c-p-item-product-selection>
                </lightning-layout-item>
            </template>

            <template lwc:if={isOutcomeIsMoneyBack}>
                <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                    <c-claim-form-refund-form cp-obj={data} full-card-data={cardData} erp-data={erpData}
                        ondatachange={handleDataChange} approval-screen={approvalScreen}></c-claim-form-refund-form>
                </lightning-layout-item>
            </template>

            
        </template>
    </lightning-layout>

    <lightning-layout multiple-rows vertical-align="stretch">
        <!-- Associated Product -->
        <template lwc:if={isIssueCategoryIsFreightRefund}>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                flexibility="auto" multiple-rows>
                <lightning-layout multiple-rows vertical-align="stretch">
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Select Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Customer Outcome"
                                options={dispatchLateOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                <abbr class="slds-required" title="required">* </abbr>
                                Freight Paid
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.itemPrice}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                <abbr class="slds-required" title="required">* </abbr>
                                Freight Refunded
                            </label>
                        </div>
                        <lightning-input data-id={data.id} data-type="input" data-fieldlabel="freightRefunded"
                            label="Freight Refunded" value={data.freightRefunded} variant="label-hidden"
                            onchange={handleChange} required type="number" step=".01" min="5"
                            max={data.itemPrice}
                            message-when-range-overflow="Freight Refunded cannot be greater than Freight Paid."
                            message-when-range-underflow="Refund amount is too low. Minimum allowed amount is $5"
                            formatter="currency" read-only={approvalScreen}>
                        </lightning-input>
                    </lightning-layout-item>
                </lightning-layout>
            </lightning-layout-item>
            
        </template>
    </lightning-layout>
</template>