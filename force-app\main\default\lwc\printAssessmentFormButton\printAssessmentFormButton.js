import { LightningElement, api, wire, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CloseActionScreenEvent } from 'lightning/actions'; // Import for closing the modal
import generatePdfContentAsBase64 from '@salesforce/apex/AssessmentFormPDFController.generatePdfContentAsBase64';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';                                           // import getRecord, getFieldValue method from uiRecordApi


//USER OBJECT FIELDS
import USER_4WD_LOCATION_FIELD from '@salesforce/schema/User.X4WD_Locations__c';                             // import User - 4WD Location Field Schema
import USER_PROFILE_NAME from '@salesforce/schema/User.Profile.Name';                                       // import User - Profile Name
import USER_NAME from '@salesforce/schema/User.Name';                                                       // import User - Name
import USER_ID from '@salesforce/user/Id';     


export default class PrintAssessmentForm<PERSON>utton extends LightningElement {
    @api recordId;
    isLoading = false; // Start with loading true since connectedCallback will fire immediately
    userID = USER_ID;                   // Current User ID.
    userLocation;                       // Current User Location 
    userProfileName;                    // Current User Profile Name
    showInternalPrintButton = false;

    /*
     * No Parameters.
     * This wire syntax is for the retrieval of the current user's 4WD Location and user data
    */
    @wire(getRecord, { recordId: '$userID', fields: [USER_4WD_LOCATION_FIELD, USER_PROFILE_NAME, USER_NAME]}) 
    getUserRecordData({ error, data }) {
        if (data) {
            this.userProfileName = getFieldValue(data, USER_PROFILE_NAME);
            this.userLocation = getFieldValue(data, USER_4WD_LOCATION_FIELD);
            let profileWithInternalPerm = ['System Administrator', 'Warehouse'];

            console.log('userProfileName -> ' + this.userProfileName);
            if(profileWithInternalPerm.includes(this.userProfileName)) {
                this.showInternalPrintButton = true;
            }
        } else if (error) {
            console.error('Error fetching Returned Location picklist values', error);
        }
    }

    connectedCallback() {
        // The timeout is to give a brief moment for the modal to render and potentially for recordId to fully propagate,
        // though for @api recordId, it should be available fairly quickly.
        // setTimeout(async () => {
        //     if (this.recordId) { // Ensure recordId is present before invoking
        //         this.fetchPdfContent();
        //     } else {
        //         console.error('PrintAssessmentFormButton: Record ID not available in connectedCallback.');
        //         this.showToast('Error', 'Record ID is missing. Cannot generate PDF.', 'error');
        //         this.isLoading = false;
        //         this.closeModal(); // Close modal if recordId is missing
        //     }
        // }, 500); // Reduced timeout slightly, adjust as needed
    }

    async fetchPdfContent(event) {
        let isCustomer = event.target.dataset.value;
        console.log('Invoke called for recordId: ' + this.recordId);
        // recordId check is already in connectedCallback before calling invoke,
        // but good to keep as invoke is an @api method.
        if (!this.recordId) {
            this.showToast('Error', 'Record ID is missing.', 'error');
            this.isLoading = false; // Ensure loading is stopped
            this.closeModal(); // Close modal if somehow invoked directly without recordId
            return;
        }

        this.isLoading = true; // Explicitly set isLoading to true when invoke starts

        try {
            const result = await generatePdfContentAsBase64({ recordId: this.recordId,isCustomer: isCustomer });
            console.log('PDF data received from Apex. Filename: ' + result.fileName);

            if (result && result.base64Data && result.fileName) {
                this.downloadPdfFromBase64(result.base64Data, result.fileName);
                // Success toast and modal close will be handled within downloadPdfFromBase64 on success
            } else {
                this.showToast('Error', 'Failed to retrieve PDF data from server.', 'error');
                this.isLoading = false; // Stop loading
                this.closeModal(); // Close modal on this specific failure
            }
        } catch (error) {
            console.error('Error in print assessment form LWC:', error);
            let errorMessage = 'An unknown error occurred while generating PDF.';
            if (error.body && error.body.message) {
                errorMessage = error.body.message;
            } else if (error.message) {
                errorMessage = error.message;
            }
            this.showToast('Error Generating PDF', errorMessage, 'error');
            this.isLoading = false; // Stop loading
            this.closeModal(); // Close modal on catch
        }
        // No finally block for isLoading or closeModal here, as it's handled in success/error paths
    }

    downloadPdfFromBase64(base64Data, fileName) {
        try {
            const sliceSize = 512;
            const byteCharacters = atob(base64Data);
            const byteArrays = [];

            for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                const slice = byteCharacters.slice(offset, offset + sliceSize);
                const byteNumbers = new Array(slice.length);
                for (let i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                byteArrays.push(byteArray);
            }

            const blob = new Blob(byteArrays, { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            console.log('PDF download triggered for: ' + fileName);
            this.showToast('Success', `${fileName} downloaded successfully.`, 'success');
            this.isLoading = false; // Stop loading
            this.closeModal(); // Close modal after successful download and toast

        } catch (e) {
            console.error('Error during client-side PDF download:', e);
            this.showToast('Download Error', 'Could not initiate PDF download.', 'error');
            this.isLoading = false; // Stop loading
            this.closeModal(); // Close modal on download error
        }
    }

    showToast(title, message, variant = 'info', mode = 'dismissable') {
        const evt = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
            mode: mode
        });
        this.dispatchEvent(evt);
    }

    closeModal() {
        this.dispatchEvent(new CloseActionScreenEvent());
        console.log('CloseActionScreenEvent dispatched.');
    }
}