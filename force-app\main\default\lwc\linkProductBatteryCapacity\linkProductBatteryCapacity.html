<!--
  @description       : 
  <AUTHOR> Aryan C
  @group             : 
  @last modified on  : 07-14-2025
  @last modified by  : Aryan C
-->
<template>
    <lightning-card>
        <!-- <h3 slot="title">
            <lightning-icon icon-name="standard:data_model" size="small"></lightning-icon>
            Assessment Fault Categories 
        </h3> -->
       
        <template if:false={loaded}>
            <lightning-spinner alternative-text="Loading"></lightning-spinner>
        </template>

        <div class="slds-p-horizontal_small">
            <div >
                <lightning-input
                    name="categoryName"
                    label="Search Product"
                    type="search"
                    value={userInput}
                    onchange={handleChange}
                ></lightning-input>
            </div>
        </div>
        <template lwc:if={showMessage}>
            <div class="slds-m-around_medium slds-p-top_small slds-text-align_center">
                <b>Product Doesn't exist, </b> 
                <!-- <lightning-button variant="brand" label="Create New Category" title="Create New Category" onclick={handleNewCategory} class="slds-m-left_small"></lightning-button> -->
            </div>
        </template>
        <template lwc:if={isSearchResultAvailable}>
            <div class="slds-m-around_medium">
                <lightning-datatable
                        key-field="Id"
                        data={searchResult}
                        columns={columns}
                        hide-checkbox-column
                        show-row-number-column
                        onrowaction={handleRowAction}>
                </lightning-datatable>
            </div>
        </template>

        
        <template lwc:if={isselectedProductsAvailable}>
            <div class="slds-m-around_medium">
                <div class="slds-text-heading_small slds-m-bottom_medium"><b>Selected Products</b></div>
                <lightning-datatable
                        key-field="Id"
                        data={selectedProducts}
                        columns={categoryColumns}
                        hide-checkbox-column
                        show-row-number-column
                        onrowaction={handleRowAction}>
                </lightning-datatable>
            </div>
        </template>

        <div slot="footer" if:true={isAnythingChanged}>
            <!-- <lightning-button label="Cancel" title="Canel" onclick={handleCancel}></lightning-button> -->
            <lightning-button variant="brand" label="Save" title="Save" onclick={handleSave} class="slds-m-left_small" ></lightning-button>
        </div>
        
    </lightning-card>
</template>