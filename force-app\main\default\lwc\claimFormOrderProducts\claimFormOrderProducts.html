<!--
    @File Name          : claimFormOrderProducts.html
    @Description        : Component for the Public Claim Form Order Products Screen.
    <AUTHOR> <PERSON>
    @Last Modified By   : Aryan C
    @Last Modified On   : 05-07-2024
    @Modification Log   :
    ==============================================================================
        Ver         Date             Author      	Modification
    ==============================================================================
        1.0      10/27/2021          ICRUZ          Initial Version
        1.1      02/22/2022          ICRUZ          [CLA-239]: Added Max Length to Description Field
        1.2      04/18/2022          ICRUZ          [CLA-73]
        1.3      06/16/2022          ICRUZ          [CLA-298]
        1.4      02/29/2024          ARYAN          [SF-410]
-->

<template>

    <!-- SPINNER -->
    <template if:true={isLoading}>
        <lightning-spinner variant="brand" alternative-text="Loading" size="medium"></lightning-spinner>
    </template>

    <!-- PAGE DESCRIPTION -->
    <lightning-layout vertical-align="stretch" multiple-rows>
        <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
            <h1>
                <span class="slds-page-header__title">Order Details</span>
            </h1>
            <p>Please select which items from your order you wish to process a claim or return on.</p>
            <p>Only one claim reason is available per claim. If you have multiple items with different reasons, then you
                will need to create separate claims.</p>
        </lightning-layout-item>
    </lightning-layout>

    <!-- ERROR MESSAGE -->
    <div data-id="orderProducts"></div>
    <template if:true={displayError}>
        <div class="slds-p-around_small">
            <c-display-message message={message} message-variant="error">
            </c-display-message>
        </div>
    </template>

    <lightning-layout vertical-align="stretch" multiple-rows>
        <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small" multiple-rows>
            <div class="slds-form">
                <div class="slds-form-element slds-form-element_horizontal">
                    <label class="slds-form-element__label">Order Id</label>
                    <div class="slds-form-element__control">
                        {orderID}
                    </div>
                </div>
                <div class="slds-form-element slds-form-element_horizontal">
                    <label class="slds-form-element__label">Email</label>
                    <div class="slds-form-element__control">
                        {email}
                    </div>
                </div>
            </div>
        </lightning-layout-item>
        <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small" multiple-rows>

                <lightning-textarea data-id="description" data-type="textarea"
                    label="Please provide a brief description of your warranty claim issue" value={descriptionValue}
                    max-length="250" message-when-too-long="Maximum of 250 characters are allowed."
                    onchange={handleChange} required>
                </lightning-textarea>
        </lightning-layout-item>
        <template if:false={isWarehouseOrStoreProfile}>
            <template if:true={erpData.custCommCheckbox}>
        <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small" multiple-rows>
            <div class="slds-p-around_medium ">
                Turn Off Customer Communications<lightning-helptext
                    content="If checked, will prevent from sending automated notifications to the customer."></lightning-helptext>
                <lightning-input type="checkbox" data-fieldname="custCommCheckbox" name="custCommCheckbox"
                    variant="label-hidden" checked={erpData.custCommCheckbox} disabled> </lightning-input>
            </div>
        </lightning-layout-item>
        </template>
    </template>

    </lightning-layout>

    <template if:true={displayCommSection}>
        <lightning-accordion active-section-name="commDetails">
            <lightning-accordion-section name="commDetails" label="Commercial Claim End User Contact Details">
                <lightning-layout vertical-align="stretch" multiple-rows>
                    <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small"
                        multiple-rows>
                        <lightning-input data-id="commName" data-type="input" label="End User Name"
                            value={commUserDetails.name} onchange={handleChange} required>
                        </lightning-input>
                        <lightning-input data-id="commEmail" data-type="input" type="email" label="End User Email"
                            value={commUserDetails.email} onchange={handleChange} required>
                        </lightning-input>
                        <lightning-input data-id="commMobile" data-type="input" type="phone" label="End User Mobile"
                            value={commUserDetails.mobile} onchange={handleChange} required>
                        </lightning-input>
                    </lightning-layout-item>
                    <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small"
                        multiple-rows>
                        <lightning-textarea data-id="commAddress" data-type="input" label="End User Address"
                            value={commUserDetails.address} max-length="255"
                            message-when-too-long="Maximum of 255 characters are allowed in the Description."
                            onchange={handleChange} required>
                        </lightning-textarea>
                        <lightning-radio-group data-id="commReplacement" data-type="radio"
                            label="End User Supplied Replacement?" options={commReplaceOptions}
                            value={commUserDetails.suppliedReplace} type="radio" onchange={handleChange} required>
                        </lightning-radio-group>
                    </lightning-layout-item>
                </lightning-layout>
            </lightning-accordion-section>
        </lightning-accordion>
    </template>

    <lightning-layout class="slds-p-around_small" horizontal-align="spread">
        <lightning-layout-item>
            <div class="slds-form-element__label">
                Order Items
            </div>
        </lightning-layout-item>
        <template if:true={displayPagination}>
            <lightning-layout vertical-align="end" horizontal-align="end">
                <lightning-input data-id="searchTerm" data-type="input" type="search" label="Search Product"
                    class="slds-p-right_x-small" value={searchTerm} placeholder="Search By Name or SKU"
                    variant="label-hidden" onkeydown={handleOnKeyDown} onchange={handleChange}>
                </lightning-input>
                <lightning-button data-id="search" variant="brand" label="Search" icon-name="utility:search"
                    onclick={handleClick}>
                </lightning-button>
            </lightning-layout>
        </template>
    </lightning-layout>

    <div data-id="tableDetails"></div>

    <lightning-layout vertical-align="stretch" multiple-rows>
        <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
            <lightning-tree-grid key-field="id" data={tableData} columns={tableColumn} expanded-rows={expandedRows}
                onrowaction={handleRowAction} hide-checkbox-column>
            </lightning-tree-grid>
        </lightning-layout-item>
        <template if:false={hasTableData}>
            <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
                <div class="slds-align_absolute-center">
                    No results found
                </div>
            </lightning-layout-item>
        </template>
        <template if:true={hasTableData}>
            <template if:true={displayPagination}>
                <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
                    <div class="slds-align_absolute-center">
                        <lightning-button-icon class="slds-p-right_small" icon-name="utility:chevronleft"
                            onclick={previousHandler}>
                        </lightning-button-icon>
                        Page {page} of {totalPage}
                        <lightning-button-icon class="slds-p-left_small" icon-name="utility:chevronright"
                            onclick={nextHandler}>
                        </lightning-button-icon>
                    </div>
                </lightning-layout-item>
            </template>
        </template>
    </lightning-layout>

    <!-- BUTTONS -->
    <lightning-layout class="slds-p-top_large slds-p-right_small" multiple-rows>
        <lightning-layout-item size="12">
            <div class="slds-clearfix">
                <!-- NEXT BUTTON -->
                <div class="slds-float_right">
                    <template if:true={showCancelFraudButton}>
    <lightning-button variant="brand" label="Cancel Fraud Order" title="Cancel Fraud Order"
        onclick={showCancelConfirmationModal} class="slds-m-left_x-small slds-m-right_x-small">
    </lightning-button>
</template>

                    <lightning-button variant="brand" label="Add All Order Items" title="Add All Order Items"
                        onclick={addAllOrderItems} class="slds-m-left_x-small slds-m-right_x-small"></lightning-button>

                    <lightning-button data-id="next" variant="brand" label="Next" onclick={handleClick}>
                    </lightning-button>

                     
                </div>
            </div>
        </lightning-layout-item>
    </lightning-layout>

   <template if:true={showCancelModal}>
    <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
        <div class="slds-modal__container">
            <!-- Header -->
            <header class="slds-modal__header slds-theme_shade slds-text-align_center">
                <h2 class="slds-modal__title slds-text-heading_medium">
                    Confirm Cancellation
                </h2>
            </header>

            <!-- Content -->
            <div class="slds-modal__content slds-p-around_medium slds-text-align_center">
                <p class="slds-text-body_regular">
                    If this order has been identified as a fraudulent order by the Finance Team, then this entire order will be cancelled from the system. There will be no refund issued. It will be flagged as a Fraud claim.
                </p>

                <!-- Spinner shown while processing -->
                <template if:true={isProcessing}>
                    <div class="slds-m-top_medium">
                        <lightning-spinner alternative-text="Processing..." size="medium"></lightning-spinner>
                        <p class="slds-text-color_weak slds-m-top_x-small">Processing cancellation, please wait...</p>
                    </div>
                </template>
            </div>

            <!-- Footer -->
            <footer class="slds-modal__footer slds-grid slds-grid_align-center slds-p-around_small">
                <lightning-button variant="neutral" label="Cancel" onclick={handleModalCancel} disabled={isProcessing}></lightning-button>
                <lightning-button variant="brand" label="Proceed" onclick={confirmCancelOrders} class="slds-m-left_small" disabled={isProcessing}></lightning-button>
            </footer>
        </div>
    </section>

    <div class="slds-backdrop slds-backdrop_open"></div>
</template>


  
  

    <template if:true={showModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_medium">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closeModalPopup}>
                        <lightning-icon icon-name="utility:close" size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>

                    <!-- Bold and Centered Heading -->
                    <h2 class="slds-text-heading_medium slds-text-align_center"
                        style="font-weight: bold; text-decoration: underline;">RTS - Returned to Sender</h2>
                    <br>
                    <h2 class="slds-text-heading_medium slds-text-align_left">When creating a case with RTS reason, all
                        items will be marked as returned and added back to the stock levels of the selected return
                        location. </h2>
                    <br>
                    <h2 class="slds-text-heading_medium slds-text-align_center">Please select a return location to
                        proceed:</h2>

                </header>



                <div class="slds-modal__content slds-p-around_medium">
                    <div class="slds-grid slds-m-bottom_medium">
                        <div class="slds-m-left_small  slds-col slds-size_3-of-6">
                            <lightning-combobox name="returnedLocation" label="Returned Location"
                                value={returnedLocationValue} placeholder="Select Location"
                                options={returnedLocationOptions} disabled={returnedLocationDisabled}
                                onchange={handleReturnedLocationChange}>
                            </lightning-combobox>
                        </div>
                    </div>
                </div>


                <footer class="slds-modal__footer">
                    <div class="slds-grid slds-grid_align-center">
                        <lightning-button variant="brand" label="Submit Claim"
                            onclick={handleSaveAction}></lightning-button>
                        <lightning-button variant="neutral" label="Cancel" onclick={cancelModal}
                            class="slds-m-left_small"></lightning-button>
                    </div>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- DISPLAY OF EDIT CLAIM QUANTITY MODAL -->
    <template if:true={showEditQty}>
        <c-edit-claim-quantity header-variant="success" header-title="Edit Quantity" table-data={items}
            selected-row={selectedRow} disabled-buttons={disableModalButtons} confirm-label="Save" cancel-label="Cancel"
            onclick={handleEditClick}>
        </c-edit-claim-quantity>
    </template>

    <!-- DISPLAY OF KNOWLEDGE ARTICLES -->
    <template if:true={displayKnowledge}>
        <c-knowledge-links selected-s-k-u={selectedSKU} product-articles-list={productArticlesList}
            onclosemodal={closeModal}>
        </c-knowledge-links>
    </template>

</template>