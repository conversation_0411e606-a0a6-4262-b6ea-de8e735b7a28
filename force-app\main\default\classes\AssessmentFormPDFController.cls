/**
 * @description       : Controller for generating and serving Assessment Form PDFs.
 * <AUTHOR> Aryan C
 * @group             :
 * @last modified on  : 07-28-2025
**/
public class AssessmentFormPDFController {

    public Assessment_Form__c assessmentForm { get; private set; }
    public String usrLocation { get; private set; }
    public String recordIdFromVf { get; private set; }
    public Boolean isPassed { get; private set; }
    


    public AssessmentFormPDFController(ApexPages.StandardController stdController) {
        this.usrLocation = [SELECT Id,X4WD_Locations__c FROM User WHERE Id =: userInfo.getUserId()].X4WD_Locations__c;
        this.assessmentForm = (Assessment_Form__c)stdController.getRecord();
        initializeAssessmentFormDetails(this.assessmentForm.Id);
    }

    public AssessmentFormPDFController() {
        Id currentRecordId = ApexPages.currentPage().getParameters().get('id');
        if (String.isNotBlank(currentRecordId)) {
            initializeAssessmentFormDetails(currentRecordId);
        } else {
            this.assessmentForm = new Assessment_Form__c(); 
        }
    }

    private void initializeAssessmentFormDetails(Id formId) {
        if (formId == null) {
            this.assessmentForm = new Assessment_Form__c(); 
            return;
        }
        system.debug('in initializeAssessmentFormDetails');
        this.recordIdFromVf = formId; 

        String query = 'SELECT ';
        Map<String, Schema.SObjectField> fieldMap = Schema.SObjectType.Assessment_Form__c.fields.getMap();
        query += String.join(new List<String>(fieldMap.keySet()), ',');
        query += ', Claim_Product__r.Product_Name__c, Claim_Product__r.SKU__c,Claim_Product__r.Assessment_Fault_Category_value__c,Claim_Product__r.Assesment_Fault_Description__c ';
        query += ' FROM Assessment_Form__c WHERE Id = :formId LIMIT 1';

        try {
            this.assessmentForm = Database.query(query.replace(':formId', '\'' + String.escapeSingleQuotes(formId) + '\''));
            if(this.assessmentForm.Assessment_Outcome__c == 'Pass'){
                this.isPassed = true;
            } else {
                this.isPassed = false;   
            }
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error loading assessment form in VF Controller: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            ApexPages.addMessage(new ApexPages.Message(ApexPages.Severity.ERROR, 'Error loading assessment form: ' + e.getMessage()));
            this.assessmentForm = new Assessment_Form__c(); 
            this.isPassed = false;
        }
    }


    @AuraEnabled(cacheable=true) 
    public static String getAssessmentFormRecordTypeDevName(Id recordId) {
        if (recordId == null) {
            return null;
        }
        try {
            Assessment_Form__c form = [SELECT RecordType.DeveloperName
                                       FROM Assessment_Form__c
                                       WHERE Id = :recordId LIMIT 1];
            return form.RecordType.DeveloperName;
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error fetching record type: ' + e.getMessage());
            throw new AuraHandledException('Error fetching record type: ' + e.getMessage());
        }
    }

    @AuraEnabled 
    public static PdfDataWrapper generatePdfContentAsBase64(Id recordId,Boolean isCustomer) {
        if (recordId == null) {
            throw new AuraHandledException('Record ID is required.');
        }

        Assessment_Form__c formDetails = [SELECT Name, RecordType.DeveloperName
                                          FROM Assessment_Form__c
                                          WHERE Id = :recordId LIMIT 1];

        String recordTypeDeveloperName = formDetails.RecordType.DeveloperName;
        String assessmentFormNameForFile = formDetails.Name.replaceAll('[^a-zA-Z0-9_\\-\\.]', '_'); // Sanitize for filename

        String vfPageName;
        if (recordTypeDeveloperName == 'Battery_Assessment_Form') {
            if(isCustomer){
                vfPageName = 'CustomerBatteryAssessmentFormPDF';
            }else{
                vfPageName = 'BatteryAssessmentFormPDF';
            }
        } else if (recordTypeDeveloperName == 'Fridge_Assessment_Form' || recordTypeDeveloperName == 'Customer_Fridge_Service_Repair_Test_Sheet') {
            vfPageName = 'FridgeAssessmentFormPDF';
        } else if (recordTypeDeveloperName == 'FCD45_60_Customer_Fridge_Service_Repair_Test_Sheet') {
            vfPageName = 'FCD45AssessmentFormPDF';
        } else {
            throw new AuraHandledException('Unsupported record type for PDF generation: ' + recordTypeDeveloperName);
        }
        
        System.debug('vfPageName ' + vfPageName);
        
        PageReference pdfPageRef = new PageReference('/apex/' + vfPageName);
        pdfPageRef.getParameters().put('id', recordId);

        Blob pdfBlob;
        String base64Pdf;

        try {
            if (Test.isRunningTest()) {
                pdfBlob = Blob.valueOf('Dummy PDF content for test context.');
            } else {
                pdfBlob = pdfPageRef.getContentAsPDF();
            }
            base64Pdf = EncodingUtil.base64Encode(pdfBlob);
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error generating PDF content: ' + e.getMessage() + ' Stack: ' + e.getStackTraceString());
            throw new AuraHandledException('Failed to generate PDF: ' + e.getMessage());
        }

        PdfDataWrapper wrapper = new PdfDataWrapper();
        wrapper.base64Data = base64Pdf;
        wrapper.fileName = assessmentFormNameForFile + '_' + vfPageName + '.pdf';
        return wrapper;
    }

    public class PdfDataWrapper {
        @AuraEnabled public String base64Data { get; set; }
        @AuraEnabled public String fileName { get; set; }
    }

    public String getRecordTypeNameForVf() {
        if (assessmentForm != null && assessmentForm.RecordTypeId != null) {
            return Schema.SObjectType.Assessment_Form__c.getRecordTypeInfosById().get(assessmentForm.RecordTypeId).getName();
        }
        return '';
    }
}