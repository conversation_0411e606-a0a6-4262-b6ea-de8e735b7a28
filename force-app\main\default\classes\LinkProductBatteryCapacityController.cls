/**
 * @description       : 
 * <AUTHOR> Aryan C
 * @group             : 
 * @last modified on  : 07-14-2025
 * @last modified by  : Aryan C
**/
public without sharing class LinkProductBatteryCapacityController {

    @AuraEnabled
    public static List<Product2> getProducts(String batteryCapacityId){
        try {
            return [SELECT Id, Name, Battrey_Capacity__c 
                        FROM Product2 
                        WHERE Battrey_Capacity__c = :batteryCapacityId];
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static List<Product2> searchProducts(String userInput){
        try {
            String serachTerm = '%' + userInput + '%';
            return [SELECT Id, Name  
                        FROM Product2 
                        WHERE Name LIKE :serachTerm];
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static string saveProdBatteryCapacity(String capacityId, List<Product2> products, List<String> deletedProds) {
        try {

            for(Product2 tempProduct : products) {
                Product_Assessment_Fault_Category__c prodCat = new Product_Assessment_Fault_Category__c();
                tempProduct.Battrey_Capacity__c = capacityId;
            }

            if(!products.isEmpty()) {
                update products;
            }

            if(deletedProds != null && deletedProds.size() > 0) {
                List<Product2> deleteProducts = new List<Product2>();
                for(String prodId : deletedProds){
                    deleteProducts.add(new Product2(Id=prodId,Battrey_Capacity__c = null));
                }
                update deleteProducts;
            }

            return 'success';
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}