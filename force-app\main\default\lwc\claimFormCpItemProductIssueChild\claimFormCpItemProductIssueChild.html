<!--
  @description       : 
  <AUTHOR> Aryan C
  @group             : 
  @last modified on  : 07-25-2025
  @last modified by  : Aryan C
-->
<template>
    <lightning-layout multiple-rows vertical-align="stretch">

        <lightning-layout-item class="slds-p-right_small slds-p-bottom_medium" size="12" large-device-size="6"
            flexibility="auto" multiple-rows>
            <lightning-layout multiple-rows vertical-align="stretch">
                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                    flexibility="auto" multiple-rows>
                    <!-- <template if:true={data.serialVisible}> -->
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                <template if:true={isSerialRequired}>
                                    <abbr class="slds-required" title="required">* </abbr>
                                </template>
                                Serial Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                                <template if:true={showPreviewSerialPhoto}> |
                                    <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                        variant="bare" alternative-text="View Serial Number Photo Location"
                                        onclick={handleOpenSerialPhotoLocationModel}></lightning-button-icon> View
                                    Serial Number Location
                                </template>
                            </label>
                        </div>
                        <!-- <template if:true={data.serialVisible}> -->
                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                label="Serial Number" value={data.serialValue} variant="label-hidden"
                                onchange={handleChange} read-only={approvalScreen} required={isSerialRequired}>
                            </lightning-input>
                            <!-- <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                            label="Serial Number" value={data.serialValue} variant="label-hidden"
                            onchange={handleChange} required={data.serialRequired} read-only={approvalScreen}>
                        </lightning-input> -->
                        <!-- </template> -->
                        <!-- <template if:false={data.serialRequired}>
                            <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.serialLabel}
                                label="Serial Number" value={data.serialValue} variant="label-hidden"
                                onchange={handleChange} required={data.serialRequired} read-only>
                            </lightning-input>
                        </template> -->

                    <!-- </template> -->
                    <!-- <template if:false={data.serialVisible}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                {data.serialLabel} <lightning-button-icon icon-name="utility:info" variant="bare"
                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                            </label>
                            <div class="slds-form-element__control">
                                N/A
                            </div>
                        </div>
                    </template> -->
                </lightning-layout-item>
                <template if:false={approvalScreen}>

                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="6" flexibility="auto" multiple-rows>
                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                            has-custom-field={trueVal} required={isSerialRequired}
                            custom-field-name="File_category_fileupload__c" custom-field-value="Serial Location Photo"
                            input-label="Photo/Video of Serial Location">
                        </c-claim-form-file-upload>
                    </lightning-layout-item>
                </template>

                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="6" large-device-size="6"
                    flexibility="auto" multiple-rows>
                    <!-- <template if:true={data.batchVisible}> -->
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                <template if:true={isBatchRequired}>
                                    <abbr class="slds-required" title="required">* </abbr>
                                </template>
                                Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon> <span style="color:red" if:false={approvalScreen}>(A batch number is required for all product faults)</span>
                                <template if:true={showPreviewBatchPhoto}> |
                                    <lightning-button-icon class="slds-m-left_small" icon-name="utility:preview"
                                        variant="bare" alternative-text="View Batch Photo Location"
                                        onclick={handleOpenBatchPhotoLocationModel}></lightning-button-icon> View Batch
                                    Photo Location
                                </template>
                            </label>
                        </div>
                        <lightning-input data-id={data.id} data-type="input" data-fieldlabel={data.batchLabel}
                            label="Batch Number" value={data.batchValue} variant="label-hidden"
                            onchange={handleChange} read-only={approvalScreen} required={isBatchRequired}>
                        </lightning-input>

                    <!-- </template>
                    <template if:false={data.batchVisible}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label">
                                Batch Number <lightning-button-icon icon-name="utility:info" variant="bare"
                                    alternative-text="Info" onclick={navigateURL}></lightning-button-icon>
                            </label>
                            <div class="slds-form-element__control">
                                N/A
                            </div>
                        </div>
                    </template> -->
                </lightning-layout-item>

                <template if:false={approvalScreen}>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                        large-device-size="6" flexibility="auto" multiple-rows>
                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                            has-custom-field={trueVal} required={isBatchRequired}
                            custom-field-name="File_category_fileupload__c" custom-field-value="Batch Location Photo"
                            input-label="Photo/Video of Batch Location">
                        </c-claim-form-file-upload>
                    </lightning-layout-item>
                </template>
            </lightning-layout>
            <!-- </lightning-layout-item> -->
        </lightning-layout-item>



        <template lwc:if={isCPReasonMissingPart}>

        </template>
        <template lwc:else>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="6"
                flexibility="auto" multiple-rows>
            </lightning-layout-item>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                flexibility="auto" multiple-rows if:false={approvalScreen}>
                <c-claim-form-file-upload card-data={data} full-card-data={cardData} uploaded-files={data.uploadedFiles}
                    onupdateddata={linkFilesToCard} required={isProductFaultPhotoVideoRequired}
                    has-custom-field={trueVal} is-video-only-upload={trueVal}
                    custom-field-name="File_category_fileupload__c" custom-field-value="Product Fault Video"
                    input-label="Product Fault Video (Please make sure that the video includes showing the batch number of the product)"  is-only-single-allowed={trueVal}>
                </c-claim-form-file-upload>
            </lightning-layout-item>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="3"
                flexibility="auto" multiple-rows if:false={approvalScreen}>
                <c-claim-form-file-upload card-data={data} full-card-data={cardData} uploaded-files={data.uploadedFiles}
                    onupdateddata={linkFilesToCard} is-photo-only-upload={trueVal}
                    required={isProductFaultPhotoVideoRequired} has-custom-field={trueVal}
                    custom-field-name="File_category_fileupload__c" custom-field-value="Product Fault Photo"
                    input-label="Product Fault Photo" is-only-single-allowed={trueVal}>
                </c-claim-form-file-upload>
            </lightning-layout-item>

        </template>
        <template if:true={isFilesUploaded}>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                flexibility="auto" multiple-rows>
                <div class="slds-form-element">
                    <span class="slds-form-element__label slds-text-title_bold" style="color: black;">
                        <template if:false={isCamper}>
                            <abbr class="slds-required" title="required">* </abbr>
                        </template>
                        Uploaded Files
                    </span>
                    <div class="slds-form-element__control">
                        <c-preview-file-thumbnails read-only={approvalScreen} record-ids={fileIds}
                            onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                    </div>
                </div>
            </lightning-layout-item>
        </template>
        <template if:true={canShowAssessmentScreen}>
            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                flexibility="auto" multiple-rows>
                <lightning-layout multiple-rows vertical-align="stretch">
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_medium" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:true={isAssesmentPossibleDisabled}>
                        <div class="slds-form-element">
                            <label class="slds-form-element__label slds-text-title_bold">
                                An assessment is required. Are you able to conduct this now? Before an outcome can be
                                given for the customer must have the assessment completed
                            </label>
                        </div>
                        <div class="slds-form-element__control">
                            {data.assesmentPossible}
                        </div>
                    </lightning-layout-item>
                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_medium" size="12"
                        large-device-size="3" flexibility="auto" multiple-rows if:false={isAssesmentPossibleDisabled}>
                        <lightning-combobox data-id={data.id} data-fieldlabel="assesmentPossible" data-type="combobox"
                            name="assesmentPossible"
                            label="An assessment is required. Are you able to conduct this now? Before an outcome can be given for the customer must have the assessment completed"
                            value={data.assesmentPossible} placeholder="Please Select"
                            options={assesmentPossibleOptions} onchange={handleChange} required
                            read-only={approvalScreen} disabled={approvalScreen}>
                        </lightning-combobox>
                    </lightning-layout-item>
                    <template lwc:if={isDoingAssessmentNow}>
                        <!-- <template if:false={hasNonDraftAssessmentForm}> -->
                            <template if:true={data.assessmentFormRequired}>
                                <template if:true={isNewAssessmentFormVisible}>
                                    <template if:true={data.isBattrey}>
                                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12" flexibility="auto" multiple-rows >
                                            <c-claim-product-progress-form-modal onremovefilefromchild={removeFileFromChild} card-data={cardData} disable-form={approvalScreen} onuploadfile={linkFilesToCard} capacity-table-info={data.battreyInfo} assessment-form={currentAssessmentForm} claim-product={data} open-from-parent={trueVal} onassessmentformsaved={handleAssessmentFormSaved} oncloseassessmentformmodal={closeAssessmentFormModal}></c-claim-product-progress-form-modal>
                                        </lightning-layout-item>
                                        
                                    </template>
                                </template>
                            </template>
                        <!-- </template> -->
                        <template if:true={showAssessmentFinalDetails}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-text-heading_small slds-text-title_bold">
                                    <hr>
                                    Assessment Details
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Assessment Fault Category
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.assessmentFaultCategoryLable}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="assessmentFaultCategory"
                                    data-type="combobox" name="assessmentFaultCategory" label="Assessment Fault Category"
                                    value={data.assessmentFaultCategory} placeholder="Select Assessment Fault Category"
                                    options={assessmentFaultCategoryOptions} onchange={handleChange} required
                                    disabled={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Assessment Fault Description
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.assessmentFaultDescription}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label">
                                        <abbr class="slds-required" title="required"
                                            if:true={showAssessmentFaultCategoryOther}>* </abbr>
                                        Assessment Fault Description <lightning-helptext class="slds-p-bottom_small"
                                            content={assessmentFaultDescriptionHelpText}></lightning-helptext>
                                    </label>
                                </div>
                                <lightning-input data-id={data.id} data-type="input"
                                    data-fieldlabel="assessmentFaultDescription" label={assessmentFaultDescriptionHelpText}
                                    value={data.assessmentFaultDescription} variant="label-hidden" onchange={handleChange}
                                    required={showAssessmentFaultCategoryOther} type="text">
                                </lightning-input>
                            </lightning-layout-item>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Assessment Outcome
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.assessmentOutcome}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="assessmentOutcome" disabled={disabledOutcome}
                                    data-type="combobox" name="assessmentOutcome" label="Assessment Outcome"
                                    value={data.assessmentOutcome} placeholder="Select Assessment Outcome"
                                    options={cpAssessmentOutcomeOptions} onchange={handleChange} required>
                                </lightning-combobox>
                            </lightning-layout-item>

                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Assessment Staff Name
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.assessmentStaffName}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label">
                                        <abbr class="slds-required" title="required">* </abbr>
                                        Assessment Staff Name
                                    </label>
                                </div>
                                <lightning-input data-id={data.id} data-type="input" data-fieldlabel="assessmentStaffName"
                                    label="Assessment Staff Name" value={data.assessmentStaffName} variant="label-hidden"
                                    onchange={handleChange} required type="text">
                                </lightning-input>
                            </lightning-layout-item>
                            <template if:true={showCPFinalOutcome}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Claim Product Final Outcome
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.claimProductFinalOutcome}
                                    </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="claimProductFinalOutcome"
                                        data-type="combobox" name="claimProductFinalOutcome" label="Claim Product Final Outcome"
                                        value={data.claimProductFinalOutcome} placeholder="Select Outcome"
                                        options={claimProductFinalOutcomeOptions} onchange={handleChange} required
                                        disabled={approvalScreen}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                                <template lwc:if={showCPItemLocation}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label slds-text-title_bold">
                                                Claim Product Item Location
                                            </label>
                                        </div>
                                        <div class="slds-form-element__control">
                                            {data.claimProductItemLocation}
                                        </div>
                                    </lightning-layout-item>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="4" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                        <lightning-combobox data-id={data.id} data-fieldlabel="claimProductItemLocation"
                                            data-type="combobox" name="claimProductItemLocation"
                                            label="Claim Product Item Location" value={data.claimProductItemLocation}
                                            placeholder="Select Outcome" options={claimProductItemLocationOptions}
                                            onchange={handleChange} required disabled={approvalScreen}>
                                        </lightning-combobox>
                                    </lightning-layout-item>
                                </template>
                            </template>

                            <template if:true={getShowReturnPalletWindow}>
                                <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                    <span><b>This item must be returned to DC in a return pallet with a return label attached to it.</b></span>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="3" flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label">
                                            Return Pallet
                                        </label>
                                    </div>
                                
                                    <c-reusuable-lookup disabled={approvalScreen}
                                        onvalueselected={handleRPSelection} onvalueremoved={handleRPRemoval} onnewrecord={handleNewReturnPallet}
                                        show-icon-on-left={trueVal} show-secondary-fields={trueVal}
                                        object-label="Return Pallet"
                                        varient-value='label-hidden' object-api-name="Return_Pallet__c" field-api-name="Name"
                                        other-field-api-name="Destination__c" 
                                        additional-field-api-name="Category__c" show-additional-field={trueVal}
                                        require-selection={trueVal} make-text-upper-case={trueVal}
                                        selected-record-id={data.Return_Pallet__c}
                                        selected-record-name={data.Return_Pallet_Name}
                                        show-new-record-option={trueVal}
                                        additional-filters={additionalFiltersForRP}>
                                    </c-reusuable-lookup>
                                </lightning-layout-item>

                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                    <div class="slds-form-element">
                                        <label class="slds-form-element__label slds-text-title_bold">
                                            Return Pallet
                                        </label>
                                    </div>
                                    <div class="slds-form-element__control">
                                        {data.Return_Pallet_Name}
                                    </div>
                                </lightning-layout-item>
                                
                            
                            </template>

                            <template if:true={showAddBackToStockLocationOptions}>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="4" flexibility="auto" multiple-rows if:true={approvalScreen}>
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label slds-text-title_bold">
                                                Add back to stock Location
                                            </label>
                                        </div>
                                        <div class="slds-form-element__control">
                                            {data.addBackToStockActionLocation}
                                        </div>
                                </lightning-layout-item>
                                <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="4"
                                    flexibility="auto" multiple-rows if:false={approvalScreen}>
                                    <lightning-combobox data-id={data.id} data-fieldlabel="userLocation"
                                        data-type="combobox" name="userLocation" label="Add back to stock Location"
                                        value={data.userLocation} placeholder="Add back to stock Location"
                                        options={addBackToStockLocationOptions} onchange={handleChange} required
                                        read-only={isAddBackToStockLocationChangeDisabled}
                                        disabled={isAddBackToStockLocationChangeDisabled}>
                                    </lightning-combobox>
                                </lightning-layout-item>
                            </template>

                            <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows
                                if:true={isClaimProductFinalOutcomeAddBackToStockParts}>
                                <c-claim-form-c-p-item-product-selection cp-obj={data} card-data={cardData}
                                    erp-data={erpData} preselectedrows={data.selectProds}
                                    onproductselectionchange={handleProductSelectionChange} show-edit-qty-button={falseVal}
                                    ondatachange={handleDataChange} approval-screen={approvalScreen}
                                    show-only-table={trueVal} item-type="addBackToStockItems"
                                    is-showing-parts-for-issue-fix={isPartsOnlyFromSparePartFix}
                                    is-fixed-as-spare-part={trueVal}
                                    is-add-back-to-stock-parts-table={trueVal}></c-claim-form-c-p-item-product-selection>
                            </lightning-layout-item>
                            <template if:true={isClaimProductFinalOutcomeProductDisposed}>
                                <template if:false={approvalScreen}>
                                    <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                        large-device-size="6" flexibility="auto" multiple-rows>

                                        <c-claim-form-file-upload card-data={data} full-card-data={cardData}
                                            uploaded-files={data.uploadedFiles} onupdateddata={linkFilesToCard}
                                            required={isDisposalPhotoRequired} has-custom-field={trueVal}
                                            custom-field-name="File_category_fileupload__c"
                                            custom-field-value="Photo of Disposed Product"
                                            input-label="Photo of Disposed Product">
                                        </c-claim-form-file-upload>
                                    </lightning-layout-item>
                                </template>
                                <!-- <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                    large-device-size="12" flexibility="auto" multiple-rows>
                                    <template if:true={isFilesUploaded}>
                                        <div class="slds-form-element">
                                            <span class="slds-form-element__label" style="color: black;">
                                                <template if:false={isCamper}>
                                                    <abbr class="slds-required" title="required">* </abbr>
                                                </template>
                                                Uploaded Files
                                            </span>
                                            <div class="slds-form-element__control">
                                                <c-preview-file-thumbnails record-ids={fileIds} read-only={approvalScreen}
                                                    onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                            </div>
                                        </div>
                                    </template>
                                </lightning-layout-item> -->
                            </template>
                        </template>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows if:false={isNewAssessmentFormVisible}>
                            <div class="slds-p-top_small">
                                <lightning-button label="Add Assessment Form" onclick={openAssessmentFormModal}
                                    if:false={approvalScreen}></lightning-button>
                                </div>
                                <template lwc:if={hasAssessmentTableData}>
                                    <lightning-datatable key-field="uuid" data={data.assessmentTableData}
                                        columns={assessmentFormColumns} hide-checkbox-column
                                        onrowaction={handleRowAction}></lightning-datatable>
                                </template>
                        </lightning-layout-item> 

                        <template if:true={showCreateDCReturn}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-text-heading_small slds-text-title_bold">
                                    <hr>
                                    Create DC Return
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-top_medium" size="12"
                                large-device-size="12" flexibility="auto" multiple-rows if:true={showDisposalWarning}>
                                <span><b>This item is unable to be disposed of at a store level for compliance reasons.
                                        Please return to a DC for its disposal</b></span>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:true={disableCustomerOutcome}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label slds-text-title_bold">
                                        Return DC location
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    {data.returnDCLocation}
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                                flexibility="auto" multiple-rows if:false={disableCustomerOutcome}>
                                <lightning-combobox data-id={data.id} data-fieldlabel="returnDCLocation"
                                    data-type="combobox" name="returnDCLocation" label="Return DC Location"
                                    value={data.returnDCLocation} placeholder="Please Select Return DC Location"
                                    options={dcReturnLocationOptions} onchange={handleChange} required
                                    read-only={approvalScreen} disabled={approvalScreen}>
                                </lightning-combobox>
                            </lightning-layout-item>

                            <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                <c-claimform-return-order cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                    ondatachange={handleDataChange} approval-screen={approvalScreen}
                                    return-related-to='dcReturn'></c-claimform-return-order>
                            </lightning-layout-item>
                        </template>

                    </template>

                    <template if:true={showCreateReturnOption}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-text-heading_small slds-text-title_bold">
                                <hr>
                            </div>
                        </lightning-layout-item>
                        
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows
                            if:true={approvalScreen}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Create a Return?
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.productReturnAction}
                            </div>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="3" flexibility="auto" multiple-rows
                            if:false={approvalScreen}>
                            <lightning-combobox data-id={data.id}
                                data-fieldlabel="productReturnAction" data-type="combobox"
                                name="productReturnAction"
                                label="Create a return?"
                                value={data.productReturnAction}
                                placeholder="Create a return?"
                                options={checkboxOptions} onchange={handleChange} required
                                read-only={disableFireRelatedStuff}>
                            </lightning-combobox>
                        </lightning-layout-item>
                       
                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows if:true={productReturnActionCreateReturn}>
                            <c-claimform-return-order cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                ondatachange={handleDataChange} approval-screen={approvalScreen}></c-claimform-return-order>
                        </lightning-layout-item>
                    </template>
                    
                    <template if:true={isAssessmentCompleted}>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-text-heading_small slds-text-title_bold">
                                <hr>
                                Customer Outcome Details
                            </div>
                        </lightning-layout-item>

                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="4"
                            flexibility="auto" multiple-rows if:true={disableCustomerOutcome}>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    Customer Outcome
                                </label>
                            </div>
                            <div class="slds-form-element__control">
                                {data.resolutionOutcome}
                            </div>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="3"
                            flexibility="auto" multiple-rows if:false={disableCustomerOutcome}>
                            <lightning-combobox data-id={data.id} data-fieldlabel="resolutionOutcome"
                                data-type="combobox" name="resolutionOutcome" label="Please Choose Customer Outcome"
                                value={data.resolutionOutcome} placeholder="Please Choose Customer Outcome"
                                options={productIssueOutcomeOptions} onchange={handleChange} required
                                read-only={approvalScreen} disabled={approvalScreen}>
                            </lightning-combobox>
                        </lightning-layout-item>

                    <template lwc:if={isAssessmentCompletedAndOutcomeIsReplacement}>
                        <template if:false={isAssessmentCompleted}>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-top_medium"
                                size="12" large-device-size="12" flexibility="auto" multiple-rows>
                                <div class="slds-text-heading_small">Customer Outcome Details</div>
                            </lightning-layout-item>
                        </template>
                        <lightning-layout-item size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <c-claim-form-c-p-item-product-selection cp-obj={data} full-card-data={cardData}
                                erp-data={erpData} preselectedrows={data.selectProds}
                                onproductselectionchange={handleProductSelectionChange} show-edit-qty-button={falseVal}
                                ondatachange={handleDataChange} approval-screen={approvalScreen}
                                is-showing-parts-for-issue-fix={isPartsOnlyFromSparePartFix}></c-claim-form-c-p-item-product-selection>
                        </lightning-layout-item>
                    </template>
                    <template lwc:if={isOutcomeIsMoneyBack}>
                        <lightning-layout-item size="12" large-device-size="9" flexibility="auto" multiple-rows>
                            <c-claim-form-refund-form cp-obj={data} full-card-data={cardData} erp-data={erpData}
                                ondatachange={handleDataChange}
                                approval-screen={approvalScreen}></c-claim-form-refund-form>
                        </lightning-layout-item>
                    </template>
                </template>
                </lightning-layout>
            </lightning-layout-item>
        </template>




    </lightning-layout>

    <template if:true={showOpenBatchPhotoLocationModel}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closePreview}>
                        <lightning-icon icon-name="utility:close" size="small" variant="inverse"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">Batch Photo Location</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium" style="text-align: center;">
                    <!-- <c-show-all-Images-of-product content-document-ids={batchPhotoLocationContentDocumentId} is-from-model={trueVal}></c-show-all-Images-of-product> -->
                    <img src={batchPhotoLocationContentDocumentId} alt={previewTitle}
                        style="max-width: 100%; max-height: 80vh;" />
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={showOpenSerialPhotoLocationModel}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closeSerialPreview}>
                        <lightning-icon icon-name="utility:close" size="small" variant="inverse"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">Serial Number Photo Location</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium" style="text-align: center;">
                    <!-- <c-show-all-Images-of-product content-document-ids={serialPhotoLocationContentDocumentId} is-from-model={trueVal}></c-show-all-Images-of-product> -->
                    <img src={serialPhotoLocationContentDocumentId} alt={previewTitle}
                        style="max-width: 100%; max-height: 80vh;" />
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
    
    <template if:true={showAddAssessmentFormModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="max-width: 75rem !important; width:75% !important">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closeAssessmentFormModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close"
                            size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">New Assessment Form</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-record-edit-form object-api-name="Assessment_Form__c">
                        <lightning-messages></lightning-messages>

                        <template lwc:if={isProductCategoryRefrigeration}>
                            <lightning-layout multiple-rows="true">
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <!-- Salesforce Claim # and Purchase Date -->
                                        <!-- <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Claim_Number__c"></lightning-input-field>
                                        </lightning-layout-item> -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Purchase_Date__c"
                                                value={erpData.order_date} readonly data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Product is in Warranty and Physical Condition -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Product_is_in_warranty__c"
                                                value={currentAssessmentForm.Product_is_in_warranty__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Physical_Condition__c"
                                                value={currentAssessmentForm.Physical_Condition__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Issue and Other -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Fridge_Issue__c"
                                                value={currentAssessmentForm.Fridge_Issue__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Other__c"
                                                value={currentAssessmentForm.Other__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Issue Occurs On and Date/Time Put on Power -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Fridge_Issue_Occurs_On__c"
                                                value={currentAssessmentForm.Fridge_Issue_Occurs_On__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">

                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Date_Time_Put_on_Power__c"
                                                value={currentAssessmentForm.Date_Time_Put_on_Power__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <!-- Temperature Set and Temperature Recorded -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Temperature_Set__c"
                                                value={currentAssessmentForm.Temperature_Set__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Temperature_Recorded__c"
                                                value={currentAssessmentForm.Temperature_Recorded__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Date/Time Recorded and Notes -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Date_Time_Recorded__c"
                                                value={currentAssessmentForm.Date_Time_Recorded__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Notes__c"
                                                value={currentAssessmentForm.Notes__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Cables -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="CIG_Cable__c"
                                                value={currentAssessmentForm.CIG_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Anderson_Cable__c"
                                                value={currentAssessmentForm.Anderson_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_Cable__c"
                                                value={currentAssessmentForm.AC_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Cover and Thermometer -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Cover__c"
                                                value={currentAssessmentForm.Cover__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Thermometer__c"
                                                value={currentAssessmentForm.Thermometer__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                            </lightning-layout>
                        </template>

                        <template lwc:elseif={isProductCategoryBushPower}>
                            <lightning-layout multiple-rows="true">
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <!-- Salesforce Claim # and Purchase Date -->
                                        <!-- <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Claim_Number__c"></lightning-input-field>
                                        </lightning-layout-item> -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Purchase_Date__c"
                                                value={erpData.order_date} readonly data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Product is in Warranty and Physical Condition -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Product_is_in_warranty__c"
                                                value={currentAssessmentForm.Product_is_in_warranty__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Physical_Condition__c"
                                                value={currentAssessmentForm.Physical_Condition__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Voltage on Arrival -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Voltage_on_Arrival__c"
                                                value={currentAssessmentForm.Voltage_on_Arrival__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- AC/DC Charge Start Time/Date and Finish Time/Date -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_DC_Charge_Start_Time_Date__c"
                                                value={currentAssessmentForm.AC_DC_Charge_Start_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_DC_Charge_Finish_Time_Date__c"
                                                value={currentAssessmentForm.AC_DC_Charge_Finish_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Rated Capacity (AH) and Load Test Start Voltage -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Rated_Capacity_AH__c"
                                                value={currentAssessmentForm.Rated_Capacity_AH__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Start_Voltage__c"
                                                value={currentAssessmentForm.Load_Test_Start_Voltage__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Discharge Amps Set on DC Load -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Discharge_Amps_Set_on_DC_Load__c"
                                                value={currentAssessmentForm.Discharge_Amps_Set_on_DC_Load__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Load Test Start Time/Date and Finish Time/Date -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Start_Time_Date__c"
                                                value={currentAssessmentForm.Load_Test_Start_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Finish_Time_Date__c"
                                                value={currentAssessmentForm.Load_Test_Finish_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Tested Capacity (AH) -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Tested_Capacity_AH__c"
                                                value={currentAssessmentForm.Tested_Capacity_AH__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                            </lightning-layout>
                        </template>
                        <template lwc:else>
                            <lightning-accordion allow-multiple-sections-open
                                active-section-name="faultReportSummary,includedAccessories,faultFoundRepaired,picturesOnFile,finalSpecificationCheck,repairInformation">

                                <lightning-accordion-section name="faultReportSummary" label="Fault Report Summary">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Fault_Report_Summary__c" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>

                                <lightning-accordion-section name="includedAccessories" label="Included Accessories">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Basket__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Handles__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Mains_IEC_Cable__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Instruction_Manual__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="DC_Lead__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Cover__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Other__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="faultFoundRepaired"
                                    label="Fault Found/Repaired/Recommendations">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Fault_Found_Repaired_Recommendations_Ref__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="picturesOnFile" label="Pictures On File">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Pictures_On_File_Of_Overall_Cosmetic__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="finalSpecificationCheck"
                                    label="Final Specification Check">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Temp_Offset_F1__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Temp_Offset_F2__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Empty_Fridge_Cool_Down_Time_Pass__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="CTR_Panel_Buttons_Display_OK__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Comp_Function_Delay_Start__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Cond_Fan_Function_Delay_Start__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="X12VDC_Operational_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="X24VDC_Operational_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Check_Interior_Light_Functions_OK__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Chk_Batt_Protect_Switch_Function__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Test_Batt_Protect_Set_To_Low__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="USB_Port_Functional_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="repairInformation" label="Repair Information">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Repair_Completed_By__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Final_Inspection_And_Test_Completed_By__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>

                            </lightning-accordion>
                        </template>

                    </lightning-record-edit-form>
                </div>
                <!-- test-->
                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Cancel"
                        onclick={closeAssessmentFormModal}></lightning-button>
                    <lightning-button variant="brand" label="Save" onclick={saveAssessmentForm}
                        class="slds-m-horizontal_medium"></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template if:true={showAddReturnPalletModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="max-width: 75rem !important; width:75% !important">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closeReturnPalletFormModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close"
                            size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">New Return Pallet</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-record-edit-form object-api-name="Return_Pallet__c" onsuccess={handleReturnPalletSuccess}>
                        <lightning-messages></lightning-messages>

                        <lightning-layout multiple-rows="true">
                            <lightning-layout-item size="12" padding="around-small">
                                <lightning-layout multiple-rows="true">
                                    
                                    <lightning-layout-item size="12" padding="around-small">
                                        <lightning-input-field field-name="From_Location__c" data-groupname="returnPalletFields"></lightning-input-field>
                                    </lightning-layout-item>

                                    <lightning-layout-item size="12" padding="around-small">
                                        <lightning-input-field field-name="Destination__c" data-groupname="returnPalletFields"></lightning-input-field>
                                    </lightning-layout-item>
                                    
                                    
                                    <lightning-layout-item size="12" padding="around-small">
                                        <lightning-input-field field-name="Category__c" data-groupname="returnPalletFields"></lightning-input-field>
                                    </lightning-layout-item>

                                    <lightning-layout-item size="12" padding="around-small">

                                    </lightning-layout-item>
                                </lightning-layout>
                            </lightning-layout-item>
                        </lightning-layout>
                        
                    </lightning-record-edit-form>
                </div>
                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Cancel"
                        onclick={closeReturnPalletFormModal}></lightning-button>
                    <lightning-button variant="brand" label="Save" onclick={saveReturnPalletForm}
                        class="slds-m-horizontal_medium"></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>