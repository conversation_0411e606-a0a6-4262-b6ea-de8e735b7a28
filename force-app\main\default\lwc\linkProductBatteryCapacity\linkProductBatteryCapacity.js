import { LightningElement, track, api } from 'lwc';
import searchProducts from '@salesforce/apex/LinkProductBatteryCapacityController.searchProducts';
import saveProdBatteryCapacity from '@salesforce/apex/LinkProductBatteryCapacityController.saveProdBatteryCapacity';
import getProducts from '@salesforce/apex/LinkProductBatteryCapacityController.getProducts';

import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import LightningConfirm from 'lightning/confirm';
import { CloseActionScreenEvent } from 'lightning/actions';
import * as ldsUtils from 'c/ldsUtils';   


/** The delay used when debouncing event handlers before invoking Apex. */
const DELAY = 500;

const columns = [
    { label: 'Product Name', fieldName: 'Name' },
    {
        type: 'button-icon',
        initialWidth: 35,
        hideDefaultActions: true,
        typeAttributes: {
            iconName: 'utility:add',
            name: 'addCategory',
            class: {
                fieldName: 'addCategory'
            }
        }
    }
];

const categoryColumns = [
    { label: 'Product Name', fieldName: 'Name' },
    { label: 'New/Existing', fieldName: 'status' },
    {
        type: 'button-icon',
        initialWidth: 35, 
        hideDefaultActions: true,
        typeAttributes: {
            iconName: 'utility:delete',
            name: 'deselectCategory',
            class: {
                fieldName: 'deselectCategory'
            }
        }
    }
];
export default class LinkProductBatteryCapacity extends LightningElement {

    @api recordId;
        @track userInput;
        @track searchResult = [];
        @track selectedProducts = [];
        columns = columns;
        categoryColumns = categoryColumns;
        @track deletedCats = [];
        @track loaded = false;
        @track showMessage = false;
    
        get isSearchResultAvailable() {
            return this.searchResult.length > 0;
        }
    
        get isselectedProductsAvailable() {
            return this.selectedProducts.length > 0;
        }
    
        get isAnythingChanged() {
            let selectedCatsToInsert = [];
            this.selectedProducts.forEach(element => {
                if(element.status == 'New') {
                    selectedCatsToInsert.push(element);
                }
            });
    
            if(selectedCatsToInsert.length == 0 && this.deletedCats == 0) {
                return false;
            }
            return true;
        }
    
        connectedCallback() {
            setTimeout(() => {
                this.fetchCategories();    
            }, 100);
            
        }
    
        fetchCategories() {
            getProducts({ batteryCapacityId: this.recordId })
            .then(result => {
                console.log('getProductsesult -> ' + JSON.stringify(result, null, 2));
                this.selectedProducts = result;
                for(let temp of this.selectedProducts){
                    temp.status = 'Existing';
                }
                this.loaded = true;
            }).catch(error => {
                console.log('error -> ' + JSON.stringify(error, null, 2));
                this.loaded = true;
                let formattedError = ldsUtils.reduceErrors(error);
                this.showToast('Error', formattedError, 'error');
            });
        }
    
        handleChange(event) {
            console.log('User  input' + event.target.value);
            this.userInput = event.target.value;
            this.search();        
        }
    
        search() {
            if(this.userInput == '') {
                this.searchResult = [];
                this.showMessage = false;
                return;
            }
            this.delayedSearch();
        }
    
        delayedSearch() {
            // Debouncing this method: Do not actually invoke the Apex call as long as this function is  
            // being called within a delay of DELAY. This is to avoid a very large number of Apex method calls.  
            window.clearTimeout(this.delayTimeout);  
            // eslint-disable-next-line @lwc/lwc/no-async-operation  
            this.delayTimeout = setTimeout(() => {  
                this.performSearch();  
            }, DELAY);  
        }
    
        performSearch() {
            this.loaded = false;
            searchProducts({ userInput: this.userInput })
            .then(result => {
                console.log('result -> ' + JSON.stringify(result, null, 2));
                this.searchResult = result;
                if(result.length > 0) {
                    this.showMessage = false;
                } else {
                    this.showMessage = true; 
                }
                this.loaded = true;
            }).catch(error => {
    
                let formattedError = ldsUtils.reduceErrors(error);
                // this.showError(formattedError);
                this.showToast('Error', formattedError, 'error');
                this.loaded = true;
            });
        }
    
        handleCancel(event) {
            this.takeCancelConfirmation();
        }
    
        handleSave(event){
            let selectedCatsToInsert = [];
            this.selectedProducts.forEach(element => {
                if(element.status == 'New') {
                    selectedCatsToInsert.push(element);
                }
            });
    
            if(selectedCatsToInsert.length == 0 && this.deletedCats == 0) {
                this.showToast('Warning', 'No categories to insert or update. Please add new categories or remove existing ones before saving.', 'warning');
                return;
            }
            this.loaded = false;
    
            saveProdBatteryCapacity({ capacityId: this.recordId, products: selectedCatsToInsert, deletedProds: this.deletedCats })
            .then(result => {
                console.log('result -> ' + JSON.stringify(result, null, 2));
                this.userInput = '';
                this.searchResult = [];
                this.showMessage = false;
                this.showToast('Succes', 'Categories saved successfully!', 'success');
                this.closeQuickAction();
                this.loaded = true;
            }).catch(error => {
                this.loaded = true;
                let formattedError = ldsUtils.reduceErrors(error);
                this.showToast('Error', formattedError, 'error');
    
            });
        }
    
        /*
         * Parameters
            * event - onrowaction event from the lightning datatable.
         * Displays the Related Knowledge Article.
        */
        async handleRowAction(event) {
    
            let actionName = event.detail?.action?.name;
            let row = event?.detail?.row;
    
            let selectedProductsLocal = JSON.parse(JSON.stringify(this.selectedProducts));
    
            if (actionName == 'addCategory') {
                let isCategoryAlreadySelected = selectedProductsLocal.some(
                    category => category.Id === row.Id
                );
        
                if (!isCategoryAlreadySelected) {
                    row.status = 'New';
                    selectedProductsLocal.push(row);
                } else {
                    console.log('Category is already selected');
                    this.showToast('Warning', 'Category is already selected', 'warning');
                }
            } else if(actionName == 'deselectCategory') {
                selectedProductsLocal = await this.takeDeSelectConfirmation(selectedProductsLocal, row);
            }
    
            this.selectedProducts = JSON.parse(JSON.stringify(selectedProductsLocal));
            console.log(' this.selectedProducts -> ' + JSON.stringify( this.selectedProducts, null, 2));
            console.log('this.deletedCats -> ' + JSON.stringify(this.deletedCats, null, 2));
        }
    
        async takeCancelConfirmation(selectedProductsLocal, row) {
    
            let selectedCatsToInsert = [];
            this.selectedProducts.forEach(element => {
                if(element.status == 'New') {
                    selectedCatsToInsert.push(element);
                }
            });
    
            if(selectedCatsToInsert.length == 0 && this.deletedCats == 0) {
                this.closeQuickAction();
            } else {
    
                const result = await LightningConfirm.open({
                    message: 'You have unsaved changes. Are you sure you want to close this modal without saving?',
                    variant: 'headerless',
                    label: 'Confirmation'
                });
                if(result) {
                    this.closeQuickAction();
                }
            }
    
          
        }
    
        async takeDeSelectConfirmation(selectedProductsLocal, row) {
            const result = await LightningConfirm.open({
                message: 'Are you sure you want to remove this category?',
                variant: 'headerless',
                label: 'Confirmation'
            });
            if(result) {
                if(row.status == 'Existing') {
                    this.deletedCats.push(row.parentId);
                }
                selectedProductsLocal = selectedProductsLocal.filter(
                    category => category.Id !== row.Id
                );
            }
    
            return selectedProductsLocal;
        }
    
    
        showToast(title, message, type ) {
            const event = new ShowToastEvent({
                title: title,
                message: message,
                variant: type
            });
            this.dispatchEvent(event);
        }
    
       
    
        closeQuickAction() {
            // this.dispatchEvent(new CloseActionScreenEvent());
        }
    
}