import { LightningElement, api, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';
import { CloseActionScreenEvent } from 'lightning/actions';
import { getRecordNotifyChange } from 'lightning/uiRecordApi';
import getClaimProductById from '@salesforce/apex/AssessmentFormController.getClaimProductById';
import createAssessmentForm from '@salesforce/apex/AssessmentFormController.createAssessmentForm';
import deleteUploadedFiles from '@salesforce/apex/LightningUtilities.deleteUploadedFiles';  
import getKnowledgeArticleById from '@salesforce/apex/AssessmentFormController.getKnowledgeArticleById';
// import updateAssessmentForm from '@salesforce/apex/AssessmentFormController.updateAssessmentForm';
// import getAssessmentFormByClaimProduct from '@salesforce/apex/AssessmentFormController.getAssessmentFormByClaimProduct';

export default class ClaimProductProgressFormModal extends LightningElement {
    showJumpStart = false;
    @track finalOutcome;
    @api disableForm = false;
    @api recordId; // Claim Product ID passed from parent or action
    @track currentStep = 'step2';
    @api claimProduct = {};
    @api openFromParent = false;api
    @track isLoading = false;
    @track errorMessage = '';
    @track existingAssessmentForm = null;
    @track isSubmitting = false;
    @track claimProductId = null;
    @api capacityTableInfo = false;

    // Timer functionality
    @track isTimerRunning = false;
    @track timerDisplay = '00:00:00';
    @track startTime = null;
    timerInterval = null;

    // Mock data for fields not available in the object
    @track returnReason = 'Assessment Fault Category drop down reasons';
    @track sparePartFix = 'No';
    @track issueDetails = 'No';
    @api cardData;

    // Assessment Form data
    @api assessmentForm = { };
    // // Assessment Form data

    yesNoOptions = [
        { label: 'Yes', value: 'Yes' },
        { label: 'No', value: 'No' }
    ];

    nowLaterOptions = [
        { label: 'Now', value: 'Now' },
        { label: 'Later', value: 'Later' }
    ];

    customerIssue = [
        { label: 'Battery is not charging', value: 'Battery is not charging' },
        { label: 'Battery is depleting too fast', value: 'Battery is depleting too fast' }
    ];

    yesNoChargingOptions = [
        { label: 'Yes - Charging Complete', value: 'Yes' },
        { label: 'No - Charging Error', value: 'No' }
    ];

    // Picklist options
    physicalConditionOptions = [
        { label: 'Poor / Very Bad', value: 'Poor / Very Bad' },
        { label: 'Used', value: 'Used' },
        { label: 'Good', value: 'Good' },
        { label: 'New', value: 'New' }
    ];

    assessmentOutcomeOptions = [
        { label: 'Pass', value: 'Pass' },
        { label: 'Fail', value: 'Fail' }
    ];

    batchFileIds = [];

    // modelOptions = [
    //     { label: 'AKFR-FR15L_01', value: 'AKFR-FR15L_01' },
    //     { label: 'AKFR-FR16L_01', value: 'AKFR-FR16L_01' },
    //     { label: 'AKFR-FR20L_A_01', value: 'AKFR-FR20L_A_01' },
    //     { label: 'AKFR-FR20L_W_01', value: 'AKFR-FR20L_W_01' },
    //     { label: 'AKFR-FR25L_A_01', value: 'AKFR-FR25L_A_01' },
    //     { label: 'AKFR-FR25L_W_01', value: 'AKFR-FR25L_W_01' }
    // ];

    isStep2Valid = false;
    isStep3Valid = false;

    // Wire to get current page reference to extract record ID from URL
    @wire(CurrentPageReference)
    getStateParameters(currentPageReference) {
        if (currentPageReference) {
            this.claimProductId = currentPageReference.state?.c__recordId || 
                                 currentPageReference.attributes?.recordId;
            
            // If no record ID from state, try to get from URL
            if (!this.claimProductId && currentPageReference.attributes?.recordId) {
                this.claimProductId = currentPageReference.attributes.recordId;
            }
            
            // If still no record ID, try to extract from URL path
            if (!this.claimProductId && currentPageReference.attributes?.objectApiName === 'Claim_Product__c') {
                const urlParts = window.location.pathname.split('/');
                const recordIndex = urlParts.findIndex(part => part === 'r');
                if (recordIndex !== -1 && urlParts[recordIndex + 2]) {
                    this.claimProductId = urlParts[recordIndex + 2];
                }
            }
            
            if (this.claimProductId) {
                this.loadClaimProduct();
                // this.loadExistingAssessmentForm();
            }
        }
    }

    // Step visibility getters
    get isStep1() {
        return this.currentStep === 'step1';
    }

    get isStep2() {
        return this.currentStep === 'step2';
    }

    get isStep3() {
        return this.currentStep === 'step3';
    }

    get isFilesUploaded() {
        if(this.fileIds.length>0) {
            return true;
        }
        return false;
    }

    get fileIds() {
        if (this.claimProduct && this.claimProduct.uploadedFiles) {
            return this.claimProduct.uploadedFiles.filter(file => file.customValue == 'Photo with multimeter') // Filter by fileName
        .map(file => file.fileID);
        }
        return [];
    }

    removeFileForProductIssue(event) {
        console.log('in cp item remove File');
        console.log('event - ', JSON.stringify(event.detail));
        let fileID = event.detail.id;

        this.deleteFile(fileID, null, true);
    }


    deleteFile(fileID, cardItemID, formatCard) {

        let cvIDList = [];

        cvIDList.push(fileID);

        deleteUploadedFiles({ cvIDList: cvIDList })
        .then((result) => {

            // let cardData = JSON.parse(JSON.stringify(this.data));
            let cardItem = JSON.parse(JSON.stringify(this.claimProduct.uploadedFiles));

            for (var i = 0; i < cardItem.length; i++) {
                if (cardItem[i].fileID === fileID) {
                    cardItem.splice(i, 1);
                }
            }
            let dataLocal = JSON.parse(JSON.stringify(this.claimProduct));
            dataLocal.uploadedFiles = cardItem;
            this.claimProduct = {};
            this.claimProduct = JSON.parse(JSON.stringify(dataLocal));
                
            let value = {
            'files': this.claimProduct.uploadedFiles
            };
            let navigateChange = new CustomEvent('removefilefromchild', {
                detail: { value }
            });

            this.dispatchEvent(navigateChange);
            
        })
        .catch((error) => {
            // this.showError(formattedError);
        });

    }

    // SLDS Progress Indicator Classes
    // get step1Class() {
    //     let baseClass = 'slds-progress__item';
    //     if (this.currentStep === 'step1') {
    //         return baseClass + ' slds-is-active';
    //     } else if (this.currentStep === 'step2' || this.currentStep === 'step3') {
    //         return baseClass + ' slds-is-completed';
    //     }
    //     return baseClass;
    // }

    get step2Class() {
        let baseClass = 'slds-progress__item';
        if (this.currentStep === 'step2') {
            return baseClass + ' slds-is-active';
        } else if (this.currentStep === 'step3') {
            return baseClass + ' slds-is-completed';
        }
        return baseClass;
    }

    get step3Class() {
        let baseClass = 'slds-progress__item';
        if (this.currentStep === 'step3') {
            return baseClass + ' slds-is-active';
        }
        return baseClass;
    }

    // Progress Bar Percentage
    get progressPercentage() {
        switch (this.currentStep) {
            case 'step1':
                return 33;
            case 'step2':
                return 66;
            case 'step3':
                return 100;
            default:
                return 0;
        }
    }

    // Progress Bar Style
    get progressBarStyle() {
        return `width: ${this.progressPercentage}%`;
    }

    // Navigation button states
    get isPreviousDisabled() {
        return this.currentStep === 'step2';
    }
    get trueVal() {
        return true;
    }

    get disableOutcome(){
        if(!this.disableForm && this.assessmentForm.Load_Test_Start_Time_Date__c && this.assessmentForm.Load_Test_Finish_Time_Date__c && 
            this.assessmentForm.Tested_Capacity_AH__c 
        ){
            return false;
        }
        return true;
    }

    get isNextDisabled() {
        if (this.isLoading || this.isSubmitting || (this.currentStep == 'step3' && this.disableForm) || (this.currentStep == 'step2' && !this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c)) {
            return true;
        }

        // Step-specific validation
        // if (this.currentStep === 'step1') {
        //     return !this.isStep1Valid;
        // } else if (this.currentStep === 'step2') {
        //     return !this.isStep2Valid;
        // } else if (this.currentStep === 'step3') {
        //     return !this.isStep3Valid;
        // }

        return false;
    }

    get nextButtonLabel() {
        if (this.currentStep === 'step3') {
            return this.isSubmitting ? 'Submitting...' : 'Submit';
        }
        return 'Next';
    }

    // Step 1 Validation - Claim Product data must be loaded
    get isStep1Valid() {
        return true;
    }

    // Overall form validation (for backward compatibility)
    get isFormValid() {
        return this.isStep1Valid && this.isStep2Valid && this.isStep3Valid;
    }

    // Validation Summary Classes and Icons
    get step1ValidationClass() {
        return this.isStep1Valid ? 'validation-summary valid' : 'validation-summary';
    }

    get step1ValidationIcon() {
        return this.isStep1Valid ? 'utility:success' : 'utility:warning';
    }

    get claimProductValidationIcon() {
        return this.isStep1Valid ? 'utility:check' : 'utility:close';
    }

    get step2ValidationClass() {
        return this.isStep2Valid ? 'validation-summary valid' : 'validation-summary';
    }

    get step2ValidationIcon() {
        return this.isStep2Valid ? 'utility:success' : 'utility:warning';
    }

    get physicalConditionValidationIcon() {
        return this.assessmentForm.Physical_Condition__c ? 'utility:check' : 'utility:close';
    }

    get fridgeIssueValidationIcon() {
        return this.assessmentForm.Fridge_Issue__c ? 'utility:check' : 'utility:close';
    }

    get modelValidationIcon() {
        return this.assessmentForm.Model__c ? 'utility:check' : 'utility:close';
    }

    get picturesValidationIcon() {
        return this.assessmentForm.Pictures_On_File_Of_Overall_Cosmetic__c ? 'utility:check' : 'utility:close';
    }

    get step3ValidationClass() {
        return this.isStep3Valid ? 'validation-summary valid' : 'validation-summary';
    }

    get step3ValidationIcon() {
        return this.isStep3Valid ? 'utility:success' : 'utility:warning';
    }

    get startTimeValidationIcon() {
        return this.assessmentForm.Load_Test_Start_Time_Date__c ? 'utility:check' : 'utility:close';
    }

    get finishTimeValidationIcon() {
        return this.assessmentForm.Load_Test_Finish_Time_Date__c ? 'utility:check' : 'utility:close';
    }

    get testedCapacityValidationIcon() {
        return this.assessmentForm.Tested_Capacity_AH__c ? 'utility:check' : 'utility:close';
    }

    get assessmentOutcomeValidationIcon() {
        return this.assessmentForm.Assessment_Outcome__c ? 'utility:check' : 'utility:close';
    }

    get testingInfoValidationIcon() {
        return this.assessmentForm.Testing_Information_Provided__c ? 'utility:check' : 'utility:close';
    }

    // Timer button states
    get stopButtonDisabled() {
        return !this.isTimerRunning;
    }

    // Timer display states
    get shouldShowTimer() {
        // Show timer if we have a start time (either running or completed)
        return this.assessmentForm.AC_DC_Charge_Start_Time_Date__c;
    }

    get isTimerCompleted() {
        // Timer is completed if we have both start and finish times
        return this.assessmentForm.AC_DC_Charge_Start_Time_Date__c &&
               this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c;
    }

    get timerStatusText() {
        if (this.isTimerCompleted) {
            return 'Total Duration';
        } else if (this.isTimerRunning) {
            return 'Timer Running';
        } else {
            return 'Timer Stopped';
        }
    }

    get timerTextClass() {
        let baseClass = 'slds-text-body_regular timer-text';
        if (this.isTimerCompleted) {
            return baseClass + ' slds-text-color_default';
        } else if (this.isTimerRunning) {
            return baseClass + ' slds-text-color_success';
        } else {
            return baseClass + ' slds-text-color_weak';
        }
    }

    // Check if timer should auto-start based on existing data
    // checkAndStartTimerIfNeeded() {
    //     // If we have a start time but no finish time, and timer is not already running
    //     if (this.assessmentForm.AC_DC_Charge_Start_Time_Date__c &&
    //         !this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c &&
    //         !this.isTimerRunning) {

    //         // Parse the start time and calculate elapsed time
    //         const startTimeString = this.assessmentForm.AC_DC_Charge_Start_Time_Date__c;
    //         // Convert datetime-local format to proper Date object
    //         this.startTime = new Date(startTimeString);

    //         // Start the timer
    //         this.isTimerRunning = true;
    //         this.timerInterval = setInterval(() => {
    //             this.updateTimerDisplay();
    //         }, 1000);

    //         console.log('Timer auto-started from existing start time:', startTimeString);
    //     } else if (this.assessmentForm.AC_DC_Charge_Start_Time_Date__c &&
    //                this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c) {
    //         // If both start and finish times exist, stop any running timer and show final duration
    //         this.stopTimerAndShowFinalDuration();
    //     }
    // }

    // Stop timer and calculate final duration between start and finish times
    // stopTimerAndShowFinalDuration() {
    //     // Stop any running timer
    //     if (this.timerInterval) {
    //         clearInterval(this.timerInterval);
    //         this.timerInterval = null;
    //     }
    //     this.isTimerRunning = false;

    //     // Calculate final duration between start and finish times
    //     if (this.assessmentForm.AC_DC_Charge_Start_Time_Date__c &&
    //         this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c) {

    //         const startTime = new Date(this.assessmentForm.AC_DC_Charge_Start_Time_Date__c);
    //         const finishTime = new Date(this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c);
    //         const duration = finishTime - startTime;

    //         if (duration > 0) {
    //             const hours = Math.floor(duration / (1000 * 60 * 60));
    //             const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    //             const seconds = Math.floor((duration % (1000 * 60)) / 1000);

    //             this.timerDisplay = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    //         } else {
    //             this.timerDisplay = '00:00:00';
    //         }

    //         console.log('Final duration calculated:', this.timerDisplay);
    //     }
    // }
    article = {};
    fetchArticles() {
        this.articles = []; // Clear previous articles
        getKnowledgeArticleById({ articleVersionName : 'Jumpstarter' }).then(result=>{
            console.log('in article-',result);
            this.article = JSON.parse(JSON.stringify(result));
        }).catch(error =>{
            console.error('Error retrieving knowledge articles', error);
            
        })
    }

    // Component initialization
    connectedCallback() {
        this.fetchArticles();
        // Priority: 1. recordId from parent, 2. URL extraction
        if (this.recordId) {
            this.claimProductId = this.recordId;
            this.loadClaimProduct();
            console.log('in api record id');

            // this.loadExistingAssessmentForm();
        }else if(this.openFromParent) {
            for(let tempFile of this.claimProduct.uploadedFiles){
                if( tempFile.customValue == "Batch Location Photo"){
                    this.batchFileIds.push(tempFile);
                }
            }
            this.finalOutcome  = this.assessmentForm.Assessment_Outcome__c;
        }else if (!this.claimProductId) {
            this.extractRecordIdFromUrl();
        }
    }

    linkFilesToCard(event) {
        console.log('in event kap',event.detail.value);
         let value = event.detail.value;

        let navigateChange = new CustomEvent('uploadfile', {
          detail: { value }
        });

        this.dispatchEvent(navigateChange);
        let tempThis = this;
        setTimeout(() => {
            let dataLocal = JSON.parse(JSON.stringify(tempThis.claimProduct));
            tempThis.claimProduct = {};
            tempThis.claimProduct = JSON.parse(JSON.stringify(dataLocal));
        }, 900);
    }

    // Extract record ID from URL as fallback
    extractRecordIdFromUrl() {
        try {
            const url = window.location.href;
            const urlParts = url.split('/');
            console.log('in extractRecordIdFromUrl');
            // Look for Claim_Product__c record pattern
            const assessmentFormIndex = urlParts.findIndex(part => part.includes('Assessment_Form__c'));
            if (assessmentFormIndex !== -1) {
            console.log('in extractRecordIdFromUrl 1');

                // Try to find record ID after the object name
                for (let i = assessmentFormIndex + 1; i < urlParts.length; i++) {
                    if (urlParts[i] && urlParts[i].length === 18 && urlParts[i].startsWith('a0')) {
                        this.claimProductId = urlParts[i];
                        this.loadClaimProduct();
                        // this.loadExistingAssessmentForm();
                        break;
                    }
                }
            }else{
            console.log('in extractRecordIdFromUrl 2');

                const claimProductIndex = urlParts.findIndex(part => part.includes('Claim_Product__c'));
                if (claimProductIndex !== -1) {
                    // Try to find record ID after the object name
                    for (let i = claimProductIndex + 1; i < urlParts.length; i++) {
                        if (urlParts[i] && urlParts[i].length === 18 && urlParts[i].startsWith('a0')) {
                            this.claimProductId = urlParts[i];
                            this.loadClaimProduct();
                            // this.loadExistingAssessmentForm();
                            break;
                        }
                    }
                }
            }
            
            if (!this.claimProductId) {
                this.errorMessage = 'Unable to determine Claim Product ID from URL. Please ensure you are on a Claim Product record page.';
            }
        } catch (error) {
            console.error('Error extracting record ID from URL:', error);
            this.errorMessage = 'Error determining Claim Product ID. Please try again.';
        }
    }

    // Load claim product data from Apex
    loadClaimProduct() {
        if (!this.claimProductId) {
            this.errorMessage = 'No Claim Product ID available';
            return;
        }

        this.isLoading = true;
        this.errorMessage = '';
        
        getClaimProductById({ recordId: this.claimProductId })
            .then(result => {
                this.claimProduct = JSON.parse(result.cpObj);
                this.isLoading = false;
                if(result.batchFileIds){
                    this.batchFileIds = [result.batchFileIds];
                }
                this.assessmentForm = JSON.parse(result.assessmentForm);
                // if(!this.assessmentForm){
                //     this.assessmentForm.AC_DC_Charge_Start_Time_Date__c = this.formatDateTimeForInput(new Date());
                // }
                console.log('Claim Product loaded:', result);
                if(this.claimProduct.battreyTableInfo){
                    this.capacityTableInfo = JSON.parse(this.claimProduct.battreyTableInfo);
                }

                // Check if timer should auto-start based on existing data
                // this.checkAndStartTimerIfNeeded();
            })
            .catch(error => {
                this.errorMessage = 'Error loading claim product: ' + (error.body?.message || error.message);
                this.isLoading = false;
                console.error('Error loading claim product:', error);
                
                // Show toast notification
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Error',
                        message: this.errorMessage,
                        variant: 'error'
                    })
                );
            });
    }

    // Load existing Assessment Form
    // loadExistingAssessmentForm() {
    //     if (!this.claimProductId) return;

    //     getAssessmentFormByClaimProduct({ claimProductId: this.claimProductId })
    //         .then(result => {
    //             if (result) {
    //                 this.existingAssessmentForm = result;
    //                 // Populate form with existing data
    //                 this.assessmentForm = {
    //                     Physical_Condition__c: result.Physical_Condition__c || '',
    //                     Fridge_Issue__c: result.Fridge_Issue__c || '',
    //                     Other__c: result.Other__c || '',
    //                     Fridge_Issue_Occurs_On__c: result.Fridge_Issue_Occurs_On__c || '',
    //                     Model__c: result.Model__c || '',
    //                     DC_Lead__c: result.DC_Lead__c || '',
    //                     Assessment_Outcome__c: result.Assessment_Outcome__c || '',
    //                     Basket__c: result.Basket__c || false,
    //                     Handles__c: result.Handles__c || false,
    //                     AC_Cable__c: result.AC_Cable__c || false,
    //                     Anderson_Cable__c: result.Anderson_Cable__c || false,
    //                     Notes__c: result.Notes__c || '',
    //                     Pictures_On_File_Of_Overall_Cosmetic__c: result.Pictures_On_File_Of_Overall_Cosmetic__c || false,
    //                     Fault_Found_Repaired_Recommendations_Ref__c: result.Fault_Found_Repaired_Recommendations_Ref__c || 'NFF',
    //                     Load_Test_Start_Time_Date__c: result.Load_Test_Start_Time_Date__c ?
    //                         result.Load_Test_Start_Time_Date__c.replace('T', ' ').substring(0, 16) : '',
    //                     Load_Test_Finish_Time_Date__c: result.Load_Test_Finish_Time_Date__c ?
    //                         result.Load_Test_Finish_Time_Date__c.replace('T', ' ').substring(0, 16) : '',
    //                     Tested_Capacity_AH__c: result.Tested_Capacity_AH__c || '',
    //                     Testing_Information_Provided__c: result.Testing_Information_Provided__c || false
    //                 };
    //             }
    //         })
    //         .catch(error => {
    //             console.error('Error loading existing Assessment Form:', error);
    //         });
    // }

    // Handle Previous button click
    handlePrevious() {
        if (this.currentStep === 'step2') {
            this.currentStep = 'step1';
        } else if (this.currentStep === 'step3') {
            this.currentStep = 'step2';
        }
    }

    // Handle Next button click
    handleNext(event) {

        if (this.currentStep === 'step1') {
            if (this.isStep1Valid) {
                this.currentStep = 'step2';
            } else {
                this.showValidationError('Please ensure all claim product data is loaded before proceeding.');
            }
        } else if (this.currentStep === 'step2') {
            this.isStep2Valid = this.checkCustomValidity('[data-groupname="step2"]');
            if (this.isStep2Valid) {
                // if(event.target.name == 'Save As Draft' && this.openFromParent){
                //     this.assessmentForm.Assessment_Outcome__c = 'Draft';
                //     this.dispatchEvent(new CustomEvent('assessmentformsaved', {
                //         detail: { assessmentData: this.assessmentForm }
                //     }));
                //     this.assessmentForm = {};
                // }else if(event.target.name == 'Save As Draft'){
                //     this.assessmentForm.Assessment_Outcome__c = 'Draft';
                //     this.createAssessment(this.assessmentForm);
                // }else{
                    this.currentStep = 'step3';
                // }
            }else {
                this.focusOnFirstErrorField();
                // this.showValidationError('Please complete all required fields before moving forward');
            }
        } else if (this.currentStep === 'step3') {
            this.isStep3Valid = this.checkCustomValidity('[data-groupname="step3"]');
            if (this.isStep3Valid) {
                this.handleSubmit();
            } else {
                this.showValidationError('Please complete all required fields');
            }
        }
    }

    // Progress Indicator Navigation Methods
    goToStep1() {
        this.currentStep = 'step1';
    }

    goToStep2() {
        // Only allow navigation to step 2 if step 1 is valid
        if (this.isStep1Valid) {
            this.currentStep = 'step2';
        } else {
            this.showValidationError('Please complete Step 1 before proceeding to Step 2.');
        }
    }

    goToStep3() {
        // Only allow navigation to step 3 if steps 1 and 2 are valid
        if (this.isStep1Valid && this.isStep2Valid) {
            this.currentStep = 'step3';
        } else {
            this.showValidationError('Please complete Steps 1 and 2 before proceeding to Step 3.');
        }
    }

    // Show validation error message
    showValidationError(message) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: 'Validation Error',
                message: message,
                variant: 'error'
            })
        );
    }

    handleFinalCheck(event) {
        this.assessmentForm.providedAllInfo = event.target.checked;
        this.dispatchEvent(new CustomEvent('assessmentformsaved', {
            detail: { assessmentData: this.assessmentForm }
        }));
    }

    // Handle Assessment Form field changes
    handleFieldChange(event) {
        const fieldName = event.target.dataset.field;
        let value;

        if (event.target.type === 'checkbox') {
            value = event.target.checked;
        } else {
            value = event.target.value;
        }

        // Update the assessment form data
        this.assessmentForm = {
            ...this.assessmentForm,
            [fieldName]: value
        };

        if(fieldName == 'Can_the_test_be_started_now_or_later__c' && value == 'Later'){
            this.assessmentForm.AC_DC_Charge_Start_Time_Date__c = null;
            this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c = null;
            this.assessmentForm.Voltage_after_charging__c = '';
            this.assessmentForm.Did_the_product_charge_correctly__c = '';
            // this.assessmentOutcomeOptions = [
            //     { label: 'Pass', value: 'Pass' },
            //     { label: 'Fail', value: 'Fail' },
            //     { label: 'Draft', value: 'Draft' }
            // ];
        } 
        if(fieldName == 'Can_the_test_be_started_now_or_later__c' && value == 'Now'){
            this.assessmentForm.Assessment_Outcome__c = '';
            // this.assessmentOutcomeOptions = [
            //     { label: 'Pass', value: 'Pass' },
            //     { label: 'Fail', value: 'Fail' }
            // ];
            
            this.assessmentForm.AC_DC_Charge_Start_Time_Date__c = this.formatDateTimeForInput(new Date());
        }

        if(fieldName == 'Assessment_Outcome__c'){
            this.finalOutcome = event.target.value;
        }

        if(this.finalOutcome && this.assessmentForm.Load_Test_Start_Time_Date__c && this.assessmentForm.Load_Test_Finish_Time_Date__c && 
            this.assessmentForm.Tested_Capacity_AH__c && this.assessmentForm.Jump_Started_Required__c && this.assessmentForm.providedAllInfo
        ){
            this.assessmentForm.Assessment_Outcome__c = this.finalOutcome;
        }else{
            // if(this.finalOutcome == 'Pass' || this.finalOutcome == 'Fail'){
            //     this.finalOutcome = '';
            // }
            this.assessmentForm.Assessment_Outcome__c = 'Draft';
        }
        this.dispatchEvent(new CustomEvent('assessmentformsaved', {
            detail: { assessmentData: this.assessmentForm }
        }));

        console.log('Field changed:', fieldName, 'Value:', value);
        console.log('Assessment Form:', this.assessmentForm);
    }

    // Handle datetime field changes
    handleDateTimeChange(event) {
        const fieldName = event.target.dataset.field;
        const value = event.target.value;

        if(fieldName == 'AC_DC_Charge_Start_Time_Date__c'){
            this.assessmentForm.AC_DC_Charge_Finish_Time_Date__c = null;
        }

        this.assessmentForm = {
            ...this.assessmentForm,
            [fieldName]: value
        };

        this.validateDateTimeInput(fieldName, value, event.target)
        if(!this.assessmentForm.Assessment_Outcome__c){
            this.assessmentForm.Assessment_Outcome__c = 'Draft';
        }

        if(this.finalOutcome && this.assessmentForm.Load_Test_Start_Time_Date__c && this.assessmentForm.Load_Test_Finish_Time_Date__c && 
            this.assessmentForm.Tested_Capacity_AH__c && this.assessmentForm.Jump_Started_Required__c && this.assessmentForm.providedAllInfo
        ){
            this.assessmentForm.Assessment_Outcome__c = this.finalOutcome;
        }else{
            //  if(this.finalOutcome == 'Pass' || this.finalOutcome == 'Fail'){
            //     this.finalOutcome = '';
            // }
            this.assessmentForm.Assessment_Outcome__c = 'Draft';
        }

        this.dispatchEvent(new CustomEvent('assessmentformsaved', {
            detail: { assessmentData: this.assessmentForm }
        }));
        // // If AC/DC start time is manually changed, check if timer should start
        // if (fieldName === 'AC_DC_Charge_Start_Time_Date__c') {
        //     // Small delay to ensure the reactive property is updated
        //     setTimeout(() => {
        //         this.checkAndStartTimerIfNeeded();
        //     }, 100);
        // }

        // If AC/DC finish time is manually changed, calculate final duration
        // if (fieldName === 'AC_DC_Charge_Finish_Time_Date__c') {
        //     // Small delay to ensure the reactive property is updated
        //     setTimeout(() => {
        //         if (value) {
        //             // If finish time is added, stop timer and show final duration
        //             this.stopTimerAndShowFinalDuration();
        //         } else {
        //             // If finish time is cleared, restart timer if start time exists
        //             this.checkAndStartTimerIfNeeded();
        //         }
        //     }, 100);
        // }

        console.log('DateTime field changed:', fieldName, 'Value:', value);
    }

    // Validate datetime input constraints
    validateDateTimeInput(fieldName, value, inputElement) {
        if (!value){
            inputElement.setCustomValidity('');
            return true;
        } 

        const inputDate = new Date(value);
        const currentDate = new Date();
        const tenSecondsAgo = new Date(currentDate.getTime() - 10 * 1000);

        // Validation for AC/DC Start Time
        if (fieldName === 'AC_DC_Charge_Start_Time_Date__c') {
            if (inputDate < tenSecondsAgo) {
                const input = this.template.querySelector('[data-field="AC_DC_Charge_Start_Time_Date__c"]');

                input.setCustomValidity('AC/DC Charge Start Time cannot be in the past. Please select a time that is not earlier than the current time.');
            }else{
                const input = this.template.querySelector('[data-field="AC_DC_Charge_Start_Time_Date__c"]');
                input.setCustomValidity('');
            }
        }

        // Validation for AC/DC Finish Time
        if (fieldName === 'AC_DC_Charge_Finish_Time_Date__c') {
            const startTime = this.assessmentForm.AC_DC_Charge_Start_Time_Date__c;
            let hasError = false;
            if (startTime) {
                const startDate = new Date(startTime);

                if (inputDate < startDate) {
                    hasError = true;
                    const input = this.template.querySelector('[data-field="AC_DC_Charge_Finish_Time_Date__c"]');

                    input.setCustomValidity('AC/DC Charge Finish Time cannot be earlier than the Start Time. Please select a time that is after the start time.');
                }else{
                    const input = this.template.querySelector('[data-field="AC_DC_Charge_Finish_Time_Date__c"]');
                    input.setCustomValidity('');
                }
            }

            if(!hasError){
                // Also check if finish time is in the future
                if (inputDate > tenSecondsAgo) {
                    const input = this.template.querySelector('[data-field="AC_DC_Charge_Finish_Time_Date__c"]');

                    input.setCustomValidity('AC/DC Charge Finish Time cannot be in the future. Please select a time that is not later than the current time.');
                }else{
                    const input = this.template.querySelector('[data-field="AC_DC_Charge_Finish_Time_Date__c"]');
                    input.setCustomValidity('');
                }
            }
        }

        return true; // Validation passed
    }

    // Handle form submission
    // handleSubmit() {
    //     if (!this.isFormValid) {
    //         this.dispatchEvent(
    //             new ShowToastEvent({
    //                 title: 'Validation Error',
    //                 message: 'Please complete all required fields before submitting.',
    //                 variant: 'error'
    //             })
    //         );
    //         return;
    //     }

    //     this.isSubmitting = true;

    //     // Prepare assessment data
    //     const assessmentData = { ...this.assessmentForm };
        
    //     // // Convert datetime strings to proper format
    //     // if (assessmentData.Load_Test_Start_Time_Date__c) {
    //     //     assessmentData.Load_Test_Start_Time_Date__c = assessmentData.Load_Test_Start_Time_Date__c.replace('.000Z', '.000+0000');
    //     // }
    //     // if (assessmentData.Load_Test_Finish_Time_Date__c) {
    //     //     assessmentData.Load_Test_Finish_Time_Date__c = assessmentData.Load_Test_Finish_Time_Date__c.replace('.000Z', '.000+0000');
    //     // }
    //     // if (assessmentData.AC_DC_Charge_Start_Time_Date__c) {
    //     //     assessmentData.AC_DC_Charge_Start_Time_Date__c = assessmentData.AC_DC_Charge_Start_Time_Date__c.replace('.000Z', '.000+0000');
    //     // }
    //     // if (assessmentData.AC_DC_Charge_Finish_Time_Date__c) {
    //     //     assessmentData.AC_DC_Charge_Finish_Time_Date__c = assessmentData.AC_DC_Charge_Finish_Time_Date__c.replace('.000Z', '.000+0000');
    //     // }

    //     // Determine if we're creating or updating
    //     // const saveMethod = this.existingAssessmentForm ? 
    //     //     updateAssessmentForm({ 
    //     //         assessmentFormId: this.existingAssessmentForm.Id, 
    //     //         assessmentData: assessmentData 
    //     //     }) :
    //     //     createAssessmentForm({ 
    //     //         claimProductId: this.claimProductId, 
    //     //         assessmentData: assessmentData 
    //     //     });
    //     console.log('assessmentData -> ' + JSON.stringify(assessmentData));
    //     console.log('this.finalOutcome -> ' + this.finalOutcome);
    //     assessmentData.Assessment_Outcome__c = this.finalOutcome;
    //     if(this.openFromParent){
    //          this.isSubmitting = false;
    //         this.dispatchEvent(new CustomEvent('assessmentformsaved', {
    //             detail: { assessmentData: assessmentData }
    //         }));
    //         this.assessmentForm = {};
    //         this.currentStep = 'step2';
    //         this.isSubmitting = false;
    //     }else{
    //         this.createAssessment(assessmentData);
    //     }
    // }

    createAssessment(assessmentData){
        createAssessmentForm({ 
                claimProductId: this.claimProductId, 
                assessmentData: JSON.stringify(assessmentData) 
            })
            .then(result => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Success',
                        message: 'Assessment form saved successfully!',
                        variant: 'success'
                    })
                );
                getRecordNotifyChange([{ recordId: this.claimProductId }]);
                
                // Close modal and refresh the page
                this.closeModal();
                
                // Dispatch custom event to refresh related list
                // this.dispatchEvent(new CustomEvent('assessmentformsaved', {
                //     detail: { assessmentFormId: result }
                // }));
                
                this.isSubmitting = false;
            })
            .catch(error => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Error',
                        message: 'Error saving Assessment Form: ' + (error.body?.message || error.message),
                        variant: 'error'
                    })
                );
                this.isSubmitting = false;
                console.error('Error saving Assessment Form:', error);
            });
    }

    // // Timer Methods
    // handleStartTimer() {
    //     if (!this.isTimerRunning) {
    //         this.isTimerRunning = true;
    //         this.startTime = new Date();

    //         // Populate AC/DC Start Time field with current time
    //         const currentDateTime = this.formatDateTimeForInput(this.startTime);
    //         this.assessmentForm = {
    //             ...this.assessmentForm,
    //             AC_DC_Charge_Start_Time_Date__c: currentDateTime,
    //             AC_DC_Charge_Finish_Time_Date__c: '',
    //         };

    //         // Start the timer interval
    //         this.timerInterval = setInterval(() => {
    //             this.updateTimerDisplay();
    //         }, 1000);

    //         console.log('Timer started at:', currentDateTime);
    //     }
    // }

    handleStopTimer() {
        console.log('in timer');
        try {
             if(!this.assessmentForm.Assessment_Outcome__c){
            this.assessmentForm.Assessment_Outcome__c = 'Draft';
        }
        let tempData = JSON.parse(JSON.stringify(this.assessmentForm));
        tempData.AC_DC_Charge_Finish_Time_Date__c = this.formatDateTimeForInput(new Date());
        this.assessmentForm = JSON.parse(JSON.stringify(tempData));

         this.dispatchEvent(new CustomEvent('assessmentformsaved', {
            detail: { assessmentData: this.assessmentForm }
        }));
        console.log('in timer',this.assessmentForm);
        } catch (error) {
            console.log('error0',error);
            console.log('error0',JSON.stringify(error));
        }
       

        // if (this.isTimerRunning) {
        //     this.isTimerRunning = false;
        //     const stopTime = new Date();

        //     // Populate AC/DC Stop Time field with current time
        //     const currentDateTime = this.formatDateTimeForInput(stopTime);
        //     this.assessmentForm = {
        //         ...this.assessmentForm,
        //         AC_DC_Charge_Finish_Time_Date__c: currentDateTime
        //     };

        //     // Clear the timer interval
        //     if (this.timerInterval) {
        //         clearInterval(this.timerInterval);
        //         this.timerInterval = null;
        //     }

        //     // Calculate and show final duration
        //     this.stopTimerAndShowFinalDuration();

        //     console.log('Timer stopped at:', currentDateTime);
        // }
    }

    // updateTimerDisplay() {
    //     if (this.startTime && this.isTimerRunning) {
    //         const now = new Date();
    //         const elapsed = now - this.startTime;

    //         const hours = Math.floor(elapsed / (1000 * 60 * 60));
    //         const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    //         const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

    //         this.timerDisplay = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    //     }
    // }

    // // Cleanup timer when component is destroyed
    // disconnectedCallback() {
    //     if (this.timerInterval) {
    //         clearInterval(this.timerInterval);
    //         this.timerInterval = null;
    //     }
    // }

    formatDateTimeForInput(date) {
        // Format date for datetime-local input: YYYY-MM-DDTHH:MM
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    handleJumpStartVideo(){
        this.showJumpStart = !this.showJumpStart;
    }

    // Close modal
    closeModal() {
        // Clean up timer before closing
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }

        // Dispatch close event to parent
        // Dispatch close event to parent
        if(this.openFromParent){
            this.dispatchEvent(new CustomEvent('closeassessmentformmodal'));
        }else{
            this.dispatchEvent(new CloseActionScreenEvent());
        }
    }

    checkCustomValidity(stepName) {
        const inputs = this.template.querySelectorAll(stepName);
        let isValid = true;

        inputs.forEach(input => {
            if (!input.checkValidity()) {
                input.reportValidity();
                isValid = false;
            }
        });

        return isValid;
    }

    // Focus on the first field that has an error
    focusOnFirstErrorField() {
        try {
            // Get all input fields in the current step
            const currentStepSelector = `[data-groupname="${this.currentStep}"]`;
            const inputs = this.template.querySelectorAll(currentStepSelector);

            // Find the first field with an error
            for (let input of inputs) {
                // Check if field is required and empty
                if (input.required && (!input.value || input.value.trim() === '')) {
                    // Focus on the first empty required field
                    input.focus();

                    // Scroll the field into view
                    input.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });

                    console.log('Focused on field:', input.dataset.field || input.name || input.label);
                    return;
                }

                // Check for custom validation errors
                if (!input.checkValidity()) {
                    input.focus();
                    input.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });

                    console.log('Focused on invalid field:', input.dataset.field || input.name || input.label);
                    return;
                }
            }

            // If no specific error found, focus on first field in current step
            if (inputs.length > 0) {
                inputs[0].focus();
                inputs[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                console.log('Focused on first field in step:', this.currentStep);
            }

        } catch (error) {
            console.error('Error focusing on field:', error);
        }
    }

    get isReturnReasonOther() {
        return this.claimProduct && this.claimProduct.assessmentFaultCategoryLable == 'Other';
    }

    get showTimerControls(){
        return this.assessmentForm && this.assessmentForm.Can_the_test_be_started_now_or_later__c == 'Now';
    }

    // get showTimerControls(){
    //     return this.assessmentForm && this.assessmentForm.Can_the_product_be_charged_now__c == 'Yes';
    // }

    get hasBatchFileIds() {
        return this.batchFileIds && this.batchFileIds.length > 0;
    }
}
