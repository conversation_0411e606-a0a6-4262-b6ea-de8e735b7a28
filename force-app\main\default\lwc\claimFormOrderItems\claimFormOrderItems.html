<!--
    @File Name          : claimFormOrderItems.html
    @Description        : Component for the Public Claim Form Order Items Screen.
    <AUTHOR> <PERSON>
    @Last Modified By   : <PERSON>
    @Last Modified On   : 07-14-2025
    @Modification Log   :
    ==============================================================================
        Ver         Date             Author      	Modification
    ==============================================================================
        1.0      10/27/2021          ICRUZ          Initial Version
        1.1      06/14/2022          ICRUZ          [CLA-299]
-->

<template>

    <!-- SPINNER -->
    <template if:true={isLoading}>
        <lightning-spinner variant="brand" alternative-text="Loading" size="medium"></lightning-spinner>
    </template>

    <template if:false={isLoading}>
        <!-- PAGE DESCRIPTION -->
        <lightning-layout vertical-align="stretch" multiple-rows>
            <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
                <h1>
                    <span class="slds-page-header__title">Claimed Item Detail</span>
                </h1>
                <p>Please select your preferred outcome for each claimed item.</p>
                <p>A photo/video of each item being claimed must be submitted.</p>
                <p>Some items require a batch or serial number to be provided which is located on the product.</p>
            </lightning-layout-item>
        </lightning-layout>

        <lightning-layout vertical-align="stretch" multiple-rows>
            <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small"
                multiple-rows>
                <div class="slds-form">
                    <div class="slds-form-element slds-form-element_horizontal">
                        <label class="slds-form-element__label slds-text-title_bold">Order Id</label>
                        <div class="slds-form-element__control">
                            {orderID}
                        </div>
                    </div>
                    <div class="slds-form-element slds-form-element_horizontal">
                        <label class="slds-form-element__label slds-text-title_bold">Email</label>
                        <div class="slds-form-element__control">
                            {email}
                        </div>
                    </div>
                </div>
            </lightning-layout-item>
            <lightning-layout-item size="12" large-device-size="6" flexibility="auto" padding="around-small"
            multiple-rows>
            <template if:false={isWarehouseOrStoreProfile}>
                <template if:true={erpData.custCommCheckbox}>
                <div class="slds-p-around_medium " >
                    Turn Off Customer Communications<lightning-helptext content="If checked, will prevent from sending automated notifications to the customer."></lightning-helptext>
                    <lightning-input type="checkbox" data-fieldname="custCommCheckbox" name="custCommCheckbox" variant="label-hidden" checked={erpData.custCommCheckbox} disabled> </lightning-input>
                </div>
            </template>
            </template>
            </lightning-layout-item>
        </lightning-layout>

        <!-- ERROR MESSAGE -->
        <!-- <div data-id="orderItems"></div> -->
        <template if:true={displayError}>
            <div class="slds-p-around_small">
                <c-display-message message={message} message-variant="error">
                </c-display-message>
            </div>
        </template>


        <template if:false={approvalScreen}>
            <lightning-layout vertical-align="stretch" multiple-rows>
                <lightning-layout-item size="12" flexibility="auto" padding="around-small" multiple-rows>
                    <b>
                        Showing item {currentClaimNumber} of {totalClaimsCount}
                    </b>
                </lightning-layout-item>
            </lightning-layout>
        </template>

        <template lwc:if={approvalScreen}>
            <!-- APPROVAL SCREEN -->
            <template for:each={cardData} for:item="data" for:index="index">
                <template if:true={data.readyForApproval}>
                    <div class="slds-card__body slds-card__body_inner" key={data.id}>
                        <article class="slds-card slds-card_boundary" style="background-color: #f3f2f2;">
                            <div class="slds-card__body slds-card__body_inner">
                                <lightning-layout multiple-rows vertical-align="stretch">
                                    <lightning-layout-item size="12" flexibility="auto" multiple-rows>
                                        <div class="slds-clearfix slds-p-bottom_small">
                                            <div class="slds-float_left">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"
                                                        style="color: black;">
                                                        Item Name
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.prodName}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </lightning-layout-item>
                                    <lightning-layout-item size="12" flexibility="auto" multiple-rows>
                                        <lightning-layout multiple-rows vertical-align="stretch">
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="3" flexibility="auto" multiple-rows
                                                if:true={approvalScreen}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"
                                                        style="color: black;">
                                                        SKU
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.sku}
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="3" flexibility="auto" multiple-rows
                                                if:false={approvalScreen}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label" style="color: black;">
                                                        SKU
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.sku}
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="3" flexibility="auto" multiple-rows
                                                if:false={isIssueCategoryIsFreightRefund}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"
                                                        style="color: black;">
                                                        Shipping Status
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.shipmentStatus}
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="3" flexibility="auto" multiple-rows
                                                if:false={approvalScreen}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label" style="color: black;">
                                                        Shipping Status
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.shipmentStatus}
                                                    </div>
                                                </div>
                                            </lightning-layout-item>

                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="2" flexibility="auto" multiple-rows>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"
                                                        style="color: black;">
                                                        Issue Category
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        {data.selectedIssueCategory}
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small" size="12"
                                                large-device-size="1" flexibility="auto" multiple-rows
                                                if:true={approvalScreen}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"  style="color: black;">
                                                        Item Price
                                                    </label>
                                                </div>
                                                <div class="slds-form-element__control">
                                                    ${data.itemPrice}
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small" size="12"
                                                large-device-size="1" flexibility="auto" multiple-rows
                                                if:false={approvalScreen}>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">
                                                        Item Price
                                                    </label>
                                                </div>
                                                <div class="slds-form-element__control">
                                                    ${data.itemPrice}
                                                </div>
                                            </lightning-layout-item>

                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="3" flexibility="auto" multiple-rows>
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label slds-text-title_bold"
                                                        style="color: black;">
                                                        Approval Status
                                                    </label>
                                                    <div class="slds-form-element__control">
                                                        <template lwc:if={data.hasApprovalStatus}>
                                                            <template lwc:if={data.isApproved}>
                                                                <span style="color: green">Approved</span>
                                                            </template>
                                                            <template lwc:elseif={data.isPendingAssessment}>
                                                                <span style="color: rgb(10, 10, 156)">Pending
                                                                    Assessment</span>
                                                            </template>
                                                            <template lwc:else>
                                                                <span style="color: red">Rejected</span>
                                                            </template>
                                                        </template>
                                                        <template lwc:else>
                                                            <lightning-button-group>
                                                                <lightning-button label="Approve" variant="success"
                                                                    icon-name="action:approval" data-id={data.id}
                                                                    data-type="approve"
                                                                    onclick={handleApproval}></lightning-button>
                                                                <lightning-button label="Reject" variant="destructive"
                                                                    icon-name="action:remove" data-id={data.id}
                                                                    data-type="reject"
                                                                    onclick={handleApproval}></lightning-button>
                                                            </lightning-button-group>
                                                        </template>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small"
                                                size="12" large-device-size="12" multiple-rows>
                                                <c-claim-form-cp-item cp-obj={data} index={index}
                                                    approval-screen={approvalScreen} erp-data={erpData}
                                                    serial-batch-url={urlName} full-card-data={cardData}
                                                    sf-case-i-d={sfCaseID} sf-claim-product-i-d={sfClaimProductID}
                                                    sf-claim-product-action-i-d={sfClaimProductActionID} is-change-resolution={isChangeResolution}
                                                    ondatachange={handleDataChange}>
                                                </c-claim-form-cp-item>
                                            </lightning-layout-item>

                                        </lightning-layout>

                                    </lightning-layout-item>
                                </lightning-layout>
                            </div>
                        </article>
                    </div>
                </template>
            </template>
        </template>
        <template lwc:else>

            <!-- ITERATE ORDER ITEMS HERE -->

            <div class="slds-card__body slds-card__body_inner" key={data.id}>
                <article class="slds-card slds-card_boundary" style="background-color: #f3f2f2;">
                    <div class="slds-card__body slds-card__body_inner">
                        <lightning-layout multiple-rows vertical-align="stretch">
                            <lightning-layout-item size="3" flexibility="auto" multiple-rows>
                                <div class="slds-clearfix slds-p-bottom_small">
                                    <div class="slds-float_left">
                                        <div class="slds-form-element">
                                            <label class="slds-form-element__label" style="color: black;">
                                                Item Name
                                            </label>
                                            <div class="slds-form-element__control">
                                                {data.prodName}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label " style="color: black;">
                                        SKU
                                    </label>
                                    <div class="slds-form-element__control">
                                        {data.sku}
                                    </div>
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                                large-device-size="3" flexibility="auto" multiple-rows>
                                <div class="slds-form-element" if:false={isIssueCategoryIsFreightRefund}>
                                    <label class="slds-form-element__label" style="color: black;">
                                        Shipping Status
                                    </label>
                                    <div class="slds-form-element__control">
                                        {data.shipmentStatus}
                                    </div>
                                </div>
                            </lightning-layout-item>
                            <lightning-layout-item class="slds-p-right_small" size="12" large-device-size="1"
                                flexibility="auto" multiple-rows>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label">
                                        Item Price
                                    </label>
                                </div>
                                <div class="slds-form-element__control">
                                    ${data.itemPrice}
                                </div>
                            </lightning-layout-item>
                        </lightning-layout>

                        <lightning-layout multiple-rows vertical-align="stretch">


                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12"
                            large-device-size="12" multiple-rows>
                            <c-claim-form-cp-item cp-obj={data} index={currentItemIndex}
                                approval-screen={approvalScreen} erp-data={erpData} serial-batch-url={urlName}
                                full-card-data={cardData} ondatachange={handleDataChange} sf-case-i-d={sfCaseID}
                                sf-claim-product-i-d={sfClaimProductID} is-change-resolution={isChangeResolution}
                                sf-claim-product-action-i-d={sfClaimProductActionID}></c-claim-form-cp-item>
                        </lightning-layout-item>

                        </lightning-layout>
                    </div>
                </article>
            </div>
        </template>

        <template if:true={displayError}>
            <div class="slds-p-around_small">
                <c-display-message message={message} message-variant="error">
                </c-display-message>
            </div>
        </template>


        <!-- BUTTONS -->
        <lightning-layout multiple-rows>
            <lightning-layout-item size="12" class="slds-p-top_large">
                <div class="slds-clearfix">


                    <!-- PREVIOUS BUTTON -->
                    <div class="slds-float_left slds-p-right_small">
                        <lightning-button data-id="previous" variant="neutral" label="Back to Product Selection"
                            onclick={handleClick}>
                        </lightning-button>
                    </div>
                    <div class="slds-float_left" if:false={isFirstClaimProduct}>
                        <lightning-button data-id="previousItem" variant="neutral" label="Previous Item"
                            onclick={handleClick}>
                        </lightning-button>
                    </div>
                    <div class="slds-float_left" if:true={approvalScreen}>
                        <lightning-button data-id="editMode" variant="neutral" label="Go Back to Edit mode"
                            onclick={handleClick}>
                        </lightning-button>
                    </div>
                    <!-- NEXT BUTTON -->
                    <template if:true={showNextButton}>
                        <div class="slds-float_right">
                            <lightning-button data-id="next" variant="brand" label={nextButtonLabel}
                                onclick={handleClick}>
                            </lightning-button>
                        </div>
                    </template>
                    <template if:true={showNextPageButton}>
                        <div class="slds-float_right">
                            <lightning-button data-id="nextPage" variant="brand" label="Next Page"
                                onclick={handleClick}>
                            </lightning-button>
                        </div>
                    </template>
                </div>
            </lightning-layout-item>
        </lightning-layout>

        <!-- CONFIRMATION DIALOG -->
        <template if:true={showDialog}>
            <c-confirmation-dialog header-variant="error" header-title="Confirmation" is-loading={modalLoading}
                body-message="All changes on this page will not be saved. Would you like to proceed?"
                disabled-buttons={disableModalButtons} confirm-label="Yes" cancel-label="No"
                onclick={handleDialogClick}>
            </c-confirmation-dialog>
        </template>
    </template>


    <template if:true={showManagerApprovalModel}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closePreview}>
                        <lightning-icon icon-name="utility:close" size="small" variant="inverse"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">Manager Code Confirmation</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium" style="text-align: center;">
                    <template if:true={isLoadingInModel}>
                        <lightning-spinner variant="brand" alternative-text="Loading" size="medium"></lightning-spinner>
                    </template>
                    <lightning-layout multiple-rows vertical-align="stretch">

                        <!-- <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-left_medium" size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-form-element">
                                <label class="slds-form-element__label slds-text-title_bold">
                                    <abbr class="slds-required" title="required">* </abbr>
                                    Manager Code
                                </label>
                            </div>
                            <lightning-input data-type="input" data-fieldlabel="managerCode" label="Manager code"
                                        value={managerCode} variant="label-hidden" required type="password" pattern="\d*"  onchange={handleInputChange}>
                            </lightning-input>
                            <div class="slds-text-color_error">{pinFeedback}</div>

                        </lightning-layout-item>
                        -->
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-left_medium"
                            size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <!-- <c-reusuable-lookup additional-filters={additionalFilters} disabled={disableManagerSelection} required={trueVal} onvalueselected={handleManagerSelection} show-icon-on-left={trueVal} show-secondary-fields={falseVal}></c-reusuable-lookup> -->
                            <lightning-combobox name="managerList" label="Select Manager" data-fieldlabel="managerName"
                                value={selectedManager} placeholder="Select Manager" options={managersList} required
                                onchange={handleManagerSelectionPicklist} disabled={disableManagerSelectionForSales}>
                            </lightning-combobox>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-left_medium"
                            size="12" large-device-size="12" flexibility="auto" multiple-rows if:true={showManagerName}>
                            <!-- <c-reusuable-lookup additional-filters={additionalFilters} disabled={disableManagerSelection} required={trueVal} onvalueselected={handleManagerSelection} show-icon-on-left={trueVal} show-secondary-fields={falseVal}></c-reusuable-lookup> -->
                            <lightning-combobox name="managerList" label="Select Manager Name"
                                data-fieldlabel="managerListName" value={selectedManagerFromList}
                                placeholder="Select Manager Name" options={storeManagersList} required
                                onchange={handleStoreManagerSelectionPicklist}>
                            </lightning-combobox>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-left_medium"
                            size="12" large-device-size="12" flexibility="auto" multiple-rows if:true={showManagerCode}>
                            <template if:false={isPinError}>
                                <div class="slds-form-element">
                                    <label class="slds-form-element__label">
                                        <abbr class="slds-required" title="required">* </abbr>
                                        Manager Code
                                    </label>
                                </div>

                                <lightning-input data-type="input" data-fieldlabel="managerCode" label="Manager code"
                                    value={managerCode} variant="label-hidden" required type="password"
                                    pattern="[0-9]{6}" maxlength="6" onchange={handleInputChange}>
                                </lightning-input>
                            </template>
                        </lightning-layout-item>
                        <lightning-layout-item class="slds-p-right_small slds-p-bottom_small slds-p-left_medium"
                            size="12" large-device-size="12" flexibility="auto" multiple-rows>
                            <div class="slds-text-color_error">{pinFeedback}</div>
                        </lightning-layout-item>
                    </lightning-layout>
                </div>
                <footer class="slds-modal__footer">
                    <template if:true={isPinError}>
                        <button class="slds-button slds-button_neutral"
                            onclick={handleManagerApprovalBack}>Back</button>
                    </template>
                    <button class="slds-button slds-button_neutral" onclick={closePreview}>Cancel</button>
                    <template if:false={isPinError}>
                        <button class="slds-button slds-button_brand" onclick={handleManagerApproval}
                            disabled={isManagerApprovalSubmissionDisabled}>Submit</button>
                    </template>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    
    <template if:true={showProcessPanel}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Processing Claim</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium" style="text-align: center;">
                    <div class="box-style">
                        <template for:each={processSteps} for:item="step" for:index="index">
                            <div class="todo-list slds-grid slds-gutters column-style slds-var-p-around_medium" key={step.label}>
                                <div class="slds-col slds-size_1-of-12 allIconHolder">
                                    <template lwc:if={step.isCompleted}>
                                        <lightning-icon icon-name="action:approval" size="x-small"></lightning-icon>
                                    </template>
                                    <template lwc:elseif={step.isProcessing}>
                                        <div class="spinnerHolder" style="margin-left: 10px;" >
                                            <lightning-spinner alternative-text="Loading" size="x-small"></lightning-spinner>
                                        </div>
                                    </template>
                                    <template lwc:elseif={step.isFailed}>
                                        <div class="slds-text-link_reset" onclick={handleErrorMessageBox}>
                                            <lightning-icon icon-name="action:close" size="x-small" data-index={index} onclick={hadleCheckErrorCalled}></lightning-icon>
                                        </div>
                                    </template>
                                    <template lwc:elseif={step.isSkipped}>
                                        <div class="slds-text-link_reset" onclick={handleErrorMessageBox}>
                                            <lightning-icon icon-name="action:lead_convert" size="x-small" data-index={index}  onclick={hadleCheckErrorCalled}></lightning-icon>
                                        </div>
                                    </template>
                                    <template lwc:else>
                                        <div class="waitHolder">
                                            <lightning-icon icon-name="utility:waits" size="x-small"></lightning-icon>
                                        </div>
                                    </template>

                                </div>
                                <div class="slds-col slds-size_1-of-12">
                                    <div class="todo__text v-text-center"  style="margin-left:2px;margin-top: 4px;">
                                        <template if:true={step.showErrorMessage}>
                                            <lightning-helptext icon-name="utility:info" content={step.errorMessage}></lightning-helptext>
                                        </template> 
                                    </div>
                                </div> 
                                <div class="slds-col slds-size_10-of-12 margin-for-processing-items">
                                    <div class="todo__text">
                                        {step.label} 
                                        <template if:true={step.showCopyToClipboard}>
                                            <lightning-button-icon
                                                icon-name="utility:copy_to_clipboard" variant="bare" 
                                                alternative-text="Copy to Clipboard" class="slds-m-left_xx-small" 
                                                title="Copy to Clipboard" data-recordnumber={step.recordnumber}
                                                onclick={handleCopy} size="medium"></lightning-button-icon>
                                        </template>
                                        <template if:true={step.showLinkToTools}>
                                            <p>
                                                <a href={step.linkToTools} target="_blank">Order Link</a>
                                            </p>
                                        </template>
                                        <template if:true={notACallCenterUser}>
                                            <template if:true={step.showPrintReturn}>
                                                <p class="slds-m-top_xx-small" style="color:#4286f4">
                                                    <template if:false={step.showIdentifictionLabel}>
                                                        <a target="_blank" onclick={handlePrintReturnLabel} data-recordid={step.cpaId}>Print Return Label</a>
                                                        <!-- <lightning-icon icon-name="utility:print" alternative-text="Print Return Label" title="Print Return Label">
                                                        </lightning-icon> -->
                                                        <lightning-button-icon icon-name="utility:print" variant="bare" alternative-text="Print Return Label" class="slds-m-left_xx-small" title="Print Return Label" data-recordnumber={step.recordnumber} size="medium" onclick={handlePrintReturnLabel}></lightning-button-icon>
                                                    </template>
                                                    <template if:true={step.showIdentifictionLabel}>
                                                        <a target="_blank" onclick={handlePrintIdentificationLabel} data-recordid={step.cpaId}>Print Identification Label</a>
                                                        <!-- <lightning-icon icon-name="utility:print" alternative-text="Print Identification Label" title="Print Identification Label">
                                                        </lightning-icon> -->
                                                        <lightning-button-icon icon-name="utility:print" variant="bare" alternative-text="Print Identification Label" class="slds-m-left_xx-small" title="Print Identification Label" data-recordnumber={step.recordnumber} size="medium" onclick={handlePrintIdentificationLabel}></lightning-button-icon>
                                                        <!-- <lightning-button label="Print Identification Label" title="Print Identification Label" icon-name="utility:print" onclick={handlePrintIdentificationLabel} data-recordid={step.cpaId} class="slds-m-left_small"></lightning-button> -->
                                                    </template>
                                                    <!-- <button data-recordid={step.cpaId} onclick={handlePrintReturnLabel}>Print Return Label</button> -->
                                                </p>
                                            </template>
                                        </template>
                                    </div>
                                </div>
                                     
                            </div>
                        </template>
                    </div>
                </div>
                <div class="slds-modal__footer" if:true={showFooterButtonsOnProcessingModal}>
                    <lightning-helptext class="slds-m-left_x-small"
                    content='Click the Print Identification Label first to print out the Claim Product label to affix to the product first.'></lightning-helptext>
                    <lightning-button variant="Neutral" label="Close" title="Close" onclick={handleCloseClaim} class="slds-m-left_x-small" if:true={showCloseClaimButton} disabled={disableClaim}></lightning-button>
                                                        
                    <!-- <lightning-button if:true={showIdentifictionLabel} variant="brand" label="Print Identification Label" onclick={handlePrintIdentificationLabel} data-recordid={step.cpaId} class="slds-m-left_x-small"></lightning-button> -->
                    <lightning-button if:true={assessmentIds} variant="brand" label="Print Completed Customer Assessment Form" title="Print Completed Customer Assessment Form" onclick={fetchPdfContent} class="slds-m-left_x-small" ></lightning-button>

                    <lightning-button variant="brand" label="View Claim" title="View Claim" onclick={handleViewClaim} class="slds-m-left_x-small" disabled={disableClaim}></lightning-button>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>

        <template if:true={showExecutePanel}>
            <c-claim-product-action-handler record-id={currentCPAId} is-called-from-process-panel={trueVal} oncpaprocesssed={handleCPAProcessCompletion} user-profile-name={userProfileName} product-qunatity-details-parent={ProductQunatityDetailsParentAfterDuplicates}></c-claim-product-action-handler>
        </template>
	</template>

    <template if:true={showNoProductErrorModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">

                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeShowNoProduct}>
                        <lightning-icon icon-name="utility:close" size="small" alternative-text="close" class="slds-button__icon slds-button__icon_large"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">No Product for Review</h2>
                </header>

                <div class="slds-modal__content slds-p-around_medium">
                    <p>We were unable to proceed because no product is eligible for review. </p>
                </div>

                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Ok" onclick={closeShowNoProduct}></lightning-button>
                </footer>
            </div>
        </section>

        <div class="slds-backdrop slds-backdrop_open"></div>
	</template>

    

    <template if:true={showPrintIdentificationLabelWindow}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">

                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closePrintIdentificationLabelWindow}>
                        <lightning-icon icon-name="utility:close" size="small" alternative-text="close" class="slds-button__icon slds-button__icon_large"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">WARRANTY CLAIM CARD</h2>
                </header>

                <div class="slds-modal__content slds-p-around_medium" style="height: 300px;">
                    <c-claim-product-label record-id={printRecordId} print-type="identification_card" hide-header={trueVal}></c-claim-product-label>
                </div>

                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Ok" onclick={closePrintIdentificationLabelWindow}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
	</template>
    <template if:true={showPrintReturnLabelWindow}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">

                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closePrintReturnLabelWindow}>
                        <lightning-icon icon-name="utility:close" size="small" alternative-text="close" class="slds-button__icon slds-button__icon_large"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                  
                    <h2 class="slds-text-heading_medium">WARRANTY RETURN LABEL</h2>
              
                </header>

                <div class="slds-modal__content slds-p-around_medium" style="height: 300px;">
                    <c-claim-product-label record-id={printRecordId} print-type="return_label" hide-header={trueVal}></c-claim-product-label>
                </div>

                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Ok" onclick={closePrintReturnLabelWindow}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
	</template>
</template>