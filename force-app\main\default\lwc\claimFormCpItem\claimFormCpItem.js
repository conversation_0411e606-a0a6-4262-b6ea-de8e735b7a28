import { LightningElement, api, track, wire } from 'lwc';

import deleteUploadedFiles from '@salesforce/apex/LightningUtilities.deleteUploadedFiles';                                  // import deleteUploadedFiles method from LightningUtilities Apex Class
import getShippingLocationList from '@salesforce/apex/LightningUtilities.getShippingLocationList';          // import getShippingLocationList method from LightningUtilities Apex Class
import getActiveCouriers from '@salesforce/apex/LightningUtilities.getActiveCouriers';                                  // import getActiveCouriers from LightningUtilities Apex Class
import getShipToAddress from '@salesforce/apex/LightningUtilities.getShipToAddress';                                    // import getShipToAddress from LightningUtilities Apex Class
import getProductInfo from '@salesforce/apex/LightningUtilities.getProductInfo';                                    // import getProductInfo from LightningUtilities Apex Class
import isValidBatchNumberApex from '@salesforce/apex/LightningUtilities.isValidBatchNumber';  
import * as ldsUtils from 'c/ldsUtils';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';                                           // import getRecord, getFieldValue method from uiRecordApi
import getProductKnowladgeData from '@salesforce/apex/LightningUtilities.getProductKnowladgeData';                      // import getProductKnowladgeData from LightningUtilities Apex Class
import getUserInformation from '@salesforce/apex/LightningUtilities.getUserInformation';
import getShippingLocations from '@salesforce/apex/LightningUtilities.getShippingLocations';                                    // import getShipToAddress from LightningUtilities Apex Class
import isCreatedByGuestUser from '@salesforce/apex/LightningUtilities.isCreatedByGuestUser';

//USER OBJECT FIELDS
import USER_4WD_LOCATION_FIELD from '@salesforce/schema/User.X4WD_Locations__c';                             // import User - 4WD Location Field Schema
import USER_PROFILE_NAME from '@salesforce/schema/User.Profile.Name';                                       // import User - Profile Name
import USER_NAME from '@salesforce/schema/User.Name';                                                       // import User - Name
import USER_ID from '@salesforce/user/Id';                                                                  // import Current User ID


const helpGuideColumns = [
    { label: 'Question', fieldName: 'Question', type: 'text' },
    { label: 'Answer', fieldName: 'Answer', type: 'text' }
];


const CP_TABLE_COLUMN = [                                                                                 // value of the claim product table columns.
    {
        label: 'Item Name',
        fieldName: 'Name',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true
    },
    {
        label: 'SKU',
        fieldName: 'SKU__c',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true
    },
    {
        label: 'Price',
        fieldName: 'price',
        type: 'currency',
        typeAttributes: {
            currencyCode: 'AUD',
            currencyDisplayAs: 'symbol'
        },
        wrapText: true,
        hideDefaultActions: true
    },
    {
        label: 'Quantity',
        fieldName: 'qty',
        type: 'number',
        wrapText: true,
        hideDefaultActions: true
    },
];


const STANDARD_DATE = new Date();                                                                                       // value of current date
STANDARD_DATE.setDate(STANDARD_DATE.getDate() + 4);

const STRING_MIN_DATE = String(STANDARD_DATE.getFullYear()).padStart(2, '0') + '-' +                                    // value of current date plus 4 days
    String((STANDARD_DATE.getMonth() + 1)).padStart(2, '0') + '-' +
    String(STANDARD_DATE.getDate()).padStart(2, '0');


export default class ClaimFormCpItem extends LightningElement {

    @track data;
    @track uploadedFiles = [];
    @track accordianOpenSection = 'TroubleshootingFAQ';
    @api erpData;
    @api serialBatchUrl;
    @track locationMap = [];
    @track locationMapRes = [];
    @track isGuest = true;

    //Resume Claim process properties
    @api sfClaimProductActionID;
    @api sfClaimProductID;
    @api sfCaseID;
    @api isChangeResolution = false;

    @api index = 0;
    @api selectedIssueCategory;
    @api approvalScreen = false;
    @api
    get cpObj() {
        return this.data;
    }
    set cpObj(value) {
        this.data = JSON.parse(JSON.stringify(value));
        this.updateReturnOrderFields();
        
    }
    @track cardData = {};
    @api
    get fullCardData() {
        return this.cardData;
    }
    set fullCardData(value) {
        this.cardData = JSON.parse(JSON.stringify(value));
        console.log('in CPITEM this.cardData -> ' + JSON.stringify(this.cardData, null, 2));
    }

    spareProducts = [{ "Id": "01t98000000EUyYAAW", "Name": "Gazebo - 4.5 x 3m", "SKU__c": "AKGA-GAZ4.5x3_01", "Length__c": 25, "Width__c": 33, "Height__c": 159, "Weight__c": 28 }, { "Id": "01t98000000EUyjAAG", "Name": "Gazebo - 4.5 x 3m - Wheeled Carry Bag (Polyester)", "SKU__c": "AKGA-4.5X3_WPBG_01", "Length__c": 14.5, "Width__c": 24.5, "Height__c": 25.5, "Weight__c": 1.9 }, { "Id": "01t98000000EUUtAAO", "Name": "Throne Camping Chair", "SKU__c": "AKTA-CAMP_CHAIR", "Length__c": 99, "Width__c": 20, "Height__c": 21, "Weight__c": 5.6 }, { "Id": "01t98000000EUiOAAW", "Name": "Clear Top Canvas Bag", "SKU__c": "AKBG-CLRTP_02", "Length__c": 17, "Width__c": 5, "Height__c": 15, "Weight__c": 0.361 }];
    @track selectedSPList = [];
    cpTableColumn = CP_TABLE_COLUMN;    // Spare Products Table Columns.
    @track selectedSPIdList = [];

    shippingLocationUpdatedByCustomLogic = false;
    @track shippingLocationOptions = [];       // Shipping Location Options.
    shippingLocationSelected = '';      // Shipping Location Value.
    standardShippingLocationId = '';    // Standard Shipping Location ID.
    @track shipToAddress = {};
    @track courierOptions = [];
    @track courierMap = {};
    @track shippingToOptions = [];
    @track shippingToList = [];

    pickupDateMin = STRING_MIN_DATE;    // Minimum Pickup Date Value.
    pickupDate = STRING_MIN_DATE;       // Selected Pickup Date Value.
    pickupTimeStart = '09:00:00.000';   // Selected Pickup Start Time Value.
    pickupTimeEnd = '17:00:00.000';     // Selected Pickup End Time Value.

    @track caseType;
    @track refundType;
    @track salesChannel;
    @track refundMethod;
    userID = USER_ID;                   // Current User ID.
    @track userLocation;                // Current User Location
    @track userProfileName;
    @track userData;
    @track userDefaultAddBackToStockLocation;
    isAddBackToStockLocationChangeDisabled = false;
    productSearchAdditionalFilter = ' Family = \'Simple\'';

    @track currentProductCategory;
    @track currentProductCategoryName;
    @track productKnowladgeData = [];
    isAddbackToStockRequired = true;


    @track faqData = '';
    @track hasFaqData = false;

    doneTypingInterval = 500;
    typingTimer;
    batchTypingTimer;

    get productSearchAdditionalFilterForSameCategory() {
        let filterQuery = ' Family = \'Simple\'';
        if (this.currentProductCategory != undefined) {
            filterQuery += ' AND Product_Category__c = \'' + this.currentProductCategory + '\'';
        }

        return filterQuery;
    }

    //Category Selection
    get isIssueCategoryIsChangeOfMind() {
        console.log('this.data.selectedIssueCategory ' + this.data.selectedIssueCategory);
        return this.data.selectedIssueCategory == '30 Day Change of Mind';
    }

    get isIssueCategoryIsProductIssue() {
        return this.data.selectedIssueCategory == 'Product Issue';
    }

    get isIssueCategoryIsTransitAndDeliveryIssue() {
        return this.data.selectedIssueCategory == 'Transit/Delivery Issue';
    }

    get isIssueCategoryIsOrderIssue() {
        return this.data.selectedIssueCategory == 'Order Issue';
    }

    get isIssueCategoryOvercharged() {
        return this.data.selectedIssueCategory == 'Overcharged';
    }

    get isIssueCategoryIsAssociatedProduct() {
        return this.data.selectedIssueCategory == 'Associated Product';
    }

    get isIssueCategoryIsFreightRefund() {
        return this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Shipping';
    }


    //Sub-Category Selection
    //Change of mind
    get COMAdvertisedAtALowerPrice() {
        return this.data.selectedOrderIssueSubCategory == 'Advertised at a lower price';
    }

    get COMProductNotAsDescribedORCOMDoesNotSuitCustomerNeeds() {
        return this.data.selectedOrderIssueSubCategory == 'Product Not As Described' || this.data.selectedIssueSubCategory == 'Does not suit customer needs';
    }

    // get COMDoesNotSuitCcustomerNeeds() {
    //     return this.data.selectedIssueSubCategory == 'Does not suit customer needs';
    // }

    get COMNotAwarePreOrderDate() {
        return this.data.selectedIssueSubCategory == 'Not aware Pre Order Date';
    }

    get COMOther() {
        return this.data.selectedIssueSubCategory == 'Other';
    }


    // Product Issue Sub Category
    get PIStoreCredit() {
        return this.data.selectedIssueSubCategory == 'Store Credit';
    }

    get PIReplacementItem() {
        return this.data.selectedIssueSubCategory == 'Replacement Item';
    }

    get PIMoneyBack() {
        return this.data.selectedIssueSubCategory == 'Money Back';
    }

    get PIDefectiveFaultyProduct() {
        return this.data.selectedIssueSubCategory == 'Defective/Faulty Product';
    }

    get PIPartMissing() {
        return this.data.selectedIssueSubCategory == 'Part Missing';
    }

    get isOutOfChangeOfMindDuration() {
        // let storeDate = Date.parse(this.data.storeShipmentDate);
        // let dcDate = Date.parse(this.data.dcShipmentDate);
        let storeDate = Date.parse(this.orderDate);
        let dcDate = Date.parse(this.orderDate);

        let storeDifferenceInDays = Math.round((new Date().getTime() - storeDate) / (1000 * 3600 * 24)); // Added parentheses around new Date().getTime() - storeDate
        let dcDifferenceInDays = Math.round((new Date().getTime() - dcDate) / (1000 * 3600 * 24)); // Added parentheses around new Date().getTime() - dcDate
        return storeDifferenceInDays > 30 || dcDifferenceInDays > 45;
    }

    get has48HourPassedSinceShipped() {
        let storeDate = Date.parse(this.orderDateTime);

        let storeDifferenceInDays = Math.round((new Date().getTime() - storeDate) / (1000 * 3600)); // Added parentheses around new Date().getTime() - storeDate
        return storeDifferenceInDays > 48;
    }

    get isProductShipped() {
        return this.data.isShipped != undefined && this.data.isShipped == true;
    }


    get isItemDispatched() {
        return this.data.isShipped != undefined && this.data.isShipped == true;
    }

    get isSKUListedAsRecalled() {
        return this.data.recalled;
    }

    get isStoreChannelSale() {
        return this.data.channelOfOrder == 'Store';
    }

    get didCauseDamage() {
        return this.data.didCauseDamage != undefined && this.data.didCauseDamage == 'Yes';
    }

    get didNotCauseDamage() {
        return this.data.didCauseDamage != undefined && this.data.didCauseDamage == 'No';
    }

    get causeDamageValueSelected() {
        return this.didCauseDamage || this.didNotCauseDamage;
    }


    get orderDateTime() {
        return this.erpData.order_date;
    }

    get orderDate() {
        let [date, time] = this.erpData.order_date.split(' ');
        return date;
    }

    get orderTime() {
        let [date, time] = this.erpData.order_date.split(' ');
        return time;
    }

    get isFilesUploaded() {
        if (this.data.uploadedFiles != undefined && this.data.uploadedFiles != [] && this.data.uploadedFiles.length > 0) {
            return true;
        }
        return false;
    }

    get trueVal() {
        return true;
    }

    get falseVal() {
        return false;
    }

    get isIssueResolved() {
        return this.data.isIssueResolved != undefined && this.data.isIssueResolved == 'Yes';
    }

    get isIssueNotResolved() {
        return this.data.isIssueResolved != undefined && this.data.isIssueResolved == 'No';
    }

    get showProductIssueChildCompFromPartMissing() {
        return this.data &&  //&& this.data.uploadedFiles && this.data.uploadedFiles.length > 0 &&
            this.data.selectedSKU && this.data.selectedSKU != '';
    }

    // Transit or Delivery Issue
    get TDIProductNnotDispatched() {
        return this.data.selectedIssueSubCategory == 'Product not dispatched';
    }

    get TDIProductDispatchedLate() {
        return this.data.selectedIssueSubCategory == 'Product dispatched late (over 48 hours)';
    }

    get TDIDamagedinTransit() {
        return this.data.selectedIssueSubCategory == 'Damaged in Transit';
    }

    get TDILostinTransit() {
        return this.data.selectedIssueSubCategory == 'Lost in Transit';
    }

    get TDIReturnedtoSender() {
        return this.data.selectedIssueSubCategory == 'Returned to Sender';
    }

    get TDIWrongProductDispatched() {
        return this.data.selectedIssueSubCategory == 'Wrong Product Dispatched/Received';
    }

    get TDIWrongProductQuantityDispatched() {
        return this.data.selectedIssueSubCategory == 'Wrong Product Quantity Dispatched/Received';
    }

    get TDIDispatchIssue() {
        return this.TDIProductNnotDispatched || this.TDIProductDispatchedLate;
    }

    // Order Issue
    get OIDuplicateOrder() {
        return this.data.selectedOrderIssueSubCategory == 'Duplicate Order';
    }

    get OIPreOrderDelays() {
        return this.data.selectedOrderIssueSubCategory == 'Pre-Order Delays';
    }

    get OIDiscontinuedProduct() {
        return this.data.selectedOrderIssueSubCategory == 'Discontinued Product(Sold Out/No Longer Available)';
    }

    get OICancelOrder() {
        return this.data.selectedOrderIssueSubCategory == 'Cancel Order';
    }

    get OIOrderdedWrongProduct() {
        return this.data.selectedOrderIssueSubCategory == 'Orderded Wrong Product';
    }

    get is72HoursBefore() {
        const orderDate = new Date(this.data.order_date);
        const currentDate = new Date();
        const diffTime = Math.abs(currentDate - orderDate);
        const diffHours = diffTime / (1000 * 60 * 60);
        if (diffHours <= 72) {
            return true;
        } else {
            return false;
        }
    }

    get isBrandNewConditionIsYes() {
        return this.data.isBrandNewCondition != undefined && this.data.isBrandNewCondition != '' && this.data.isBrandNewCondition == 'Yes';
    }
    get isBrandNewConditionIsNo() {
        return this.data.isBrandNewCondition != undefined && this.data.isBrandNewCondition != '' && this.data.isBrandNewCondition == 'No';
    }

    get isReturnMethodIsPrepaidConsignment() {
        return this.data.returnMethod != undefined && this.data.returnMethod == 'Prepaid Consignment';
    }

    get isReturnRequired() {
        return this.data?.addBackToStockAction != undefined && this.data?.addBackToStockAction == 'Create Return';
    }

    get showIssueNotResolvedOptions() {
        return (this.data.isIssueResolved == true ||
            (
                this.didNotCauseDamage &&
                this.data.willSparePartCanFixIssue != undefined &&
                (
                    this.data.willSparePartCanFixIssue == 'Yes' ||
                    this.data.willSparePartCanFixIssue == 'No'
                )
            )
            ||
            !this.didNotCauseDamage
        );
    }

    /*
     * No Parameters.
     * Populate the Street Field for the Shipping To Address Field.
    */
    get streetName() {
        return this.data?.shipToAddress?.street[0];
    }

    get isCourierBorderExpress() {
        return this.isReturnMethodIsPrepaidConsignment && this.data?.courierName == 'Border Express';
    }

    get isOutcomeIsMoneyBack() {
        return this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '' && this.data.resolutionOutcome == 'Money Back' && this.isCOMProcessSelection != true;
    }

    get isOutcomeIsReplacement() {
        return this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Item' || this.data.resolutionOutcome == 'Product Quantity Replaced') && this.isCOMProcessSelection != true;
    }

    get isOutcomeIsReplacementAny() {
        return this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Item' || this.data.resolutionOutcome == 'Product Quantity Replaced' || this.data.resolutionOutcome == 'Replacement Part') && this.isCOMProcessSelection != true;
    }

    get isOutcomeIsReplacementItemSameCategory() {
        return this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Item(Same Category)');
    }

    get showOutcomeIsReplacementItemSameCategoryTable() {
        return this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Item(Same Category)') && this.data.CP_Replacement_SKU_Id__c != undefined && this.data.CP_Replacement_SKU_Id__c != '';
    }

    get isReturnReasonIsOther() {
        return this.data.whyHasTheProductBeenReturned != undefined && this.data.whyHasTheProductBeenReturned == 'Other';
    }

    get ifManual() {
        return this.data.refundType == 'Manual' && this.data.caseType != 'Commercial' ? true : false;
    }

    get proceedWithCancelOrder() {
        return this.data.proceedWithCancelOrder == 'Yes' ? true : false;
    }

    get addBackToStockActionTypeNow() {
        return this.data.addBackToStockAction == 'Now';
    }

    get restockFee() {
        return ((this.data.itemPrice * 10) / 100).toFixed(2);
    }

    get addBackToStockOptions() {
        if (this.userProfileName == 'Warehouse' && this.isIssueCategoryIsTransitAndDeliveryIssue == true) {
            return  [{ label: 'Now', value: 'Now' }];
        } 
        console.log('in getAddbacktooptions -> ' + JSON.stringify(this.userProfileName, null, 2));
        let claimProfile =['Claim User', 'Claim Manager', 'Claims Agent', 'Claims Management Profile'];
        if(this.userProfileName != undefined && claimProfile.includes(this.userProfileName) ) {
            return [
                {
                    label: 'Create Return',
                    value: 'Create Return'
                }
            ];
        }

        let storeProfile =['Store User', 'Store Manager'];
        if(this.userProfileName != undefined && storeProfile.includes(this.userProfileName) ) {
            return [
                {
                    label: 'Now',
                    value: 'Now'
                }
            ];
        }
        
        return [
            {
                label: 'Now',
                value: 'Now'
            },
            {
                label: 'Create Return',
                value: 'Create Return'
            }
        ];

    }


    get transitAndDeliveryIssueSubCategoryOptions() {
        // return (this.userProfileName == 'Warehouse' || this.userProfileName == 'System Administrator') ? this.transitAndDeliveryIssueSubCategoryOptionsForWarehouse : this.transitAndDeliveryIssueSubCategoryOptionsForStore;
        let options;

        if(this.data != undefined && this.data.selectedIssueCategory != undefined && this.data.selectedIssueSubCategory != undefined 
            && this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Shipping') {
                return [
                    {
                        label: 'Shipping',
                        value: 'Shipping'
                    }
                ];
        }
        
        if (this.userProfileName == 'Warehouse') {
            options = [...this.transitAndDeliveryIssueSubCategoryOptionsForWarehouseOnly];
            // Remove Create Return from the addBackToStockOptions
            // this.addBackToStockOptions = [{ label: 'Now', value: 'Now' }];
            this.data.addBackToStockAction = 'Now';
            this.data.addBackToStockActionLocation = this.data.addBackToStockActionLocation != undefined ? this.data.addBackToStockActionLocation : this.userDefaultAddBackToStockLocation;
            if(this.data.userLocation == undefined) {
                this.data.userLocation = this.data.addBackToStockActionLocation;
            }
            this.data.userLocationName = this.data.addBackToStockActionLocation; //this.shippingToOptions.find(opt => opt.value === this.data.addBackToStockActionLocation).label;
        } else if (this.userProfileName == 'System Administrator') {
            options = [...this.transitAndDeliveryIssueSubCategoryOptionsForWarehouse];
        } else {
            options = [...this.transitAndDeliveryIssueSubCategoryOptionsForStore];
        }

        if (this.isItemDispatched) {
            options = options.filter(option => option.value !== 'Product not dispatched' && option.value !== 'Product dispatched late (over 48 hours)');
        } else {
            options = [
                {
                    "label": "Product not dispatched",
                    "value": "Product not dispatched"
                },
                {
                    "label": "Product dispatched late (over 48 hours)",
                    "value": "Product dispatched late (over 48 hours)"
                }
            ]
        }

        return options;
    }

    get hideOrderIssueSubCatOptions() {
        return this.approvalScreen == true ||
            (this.data.disableOrderIssueSubCategoryPicklist != undefined && this.data.disableOrderIssueSubCategoryPicklist == true);
    }

    get hideSparePartTable() {
        return this.approvalScreen || this.data.willSparePartCanFixIssue == 'No';
    }

    get sparePartCanFix() {
        return this.data.willSparePartCanFixIssue != undefined && this.data.willSparePartCanFixIssue == 'Yes';
    }

    get isSpareNotAvailableInTable() {
        return this.data.isSpareAvailableInTable != undefined && this.data.isSpareAvailableInTable == 'No';
    }
    get isNoPartsRequired(){
        if(this.data.isSpareAvailableInTable == 'undefined' || this.data.isSpareAvailableInTable == '' ){
            return true;
        }
        else if(this.data.isSpareAvailableInTable == 'No'){
            return false;
        }
        else{
            return true;
        }
       
    }



    get fileIds() {
        if (this.data && this.data.uploadedFiles) {
            return this.data.uploadedFiles.map(file => file.fileID);
        }
        return [];
    }

    get isAssessmentCompleted() {
        // Check if all required data is present and not empty
        return this.data &&
            (
                (
                    (
                        this.data.assessmentOutcome != undefined & this.data.assessmentOutcome != '' &&
                        // this.data.assessmentFaultDescription != undefined & this.data.assessmentFaultDescription != '' &&
                        this.data.assessmentStaffName != undefined & this.data.assessmentStaffName != ''
                    )
                    ||
                    this.TDIDamagedinTransit
                )
            )
    }

    get showCreateDCReturn() {
        return ( 
                    (this.isIssueCategoryIsProductIssue == true && this.isAssessmentCompleted && this.data.claimProductFinalOutcome == 'Product Disposed') 
                    || this.isIssueCategoryIsProductIssue == false
                )
            && this.data.warrantyClaimDCDisposalCompulsory == true 
            && this.userProfileName != 'Warehouse';

        // let failedOutcomes = ['Genuine Fault', 'Known Fault', 'Product Fault'];

        // return (
        //     (this.isAssessmentCompleted && this.data.warrantyClaimDCDisposalCompulsory == true && this.data.claimProductFinalOutcome == 'Product Disposed' && this.sfClaimProductActionID == undefined && this.userProfileName != 'Warehouse')
        //     ||
        //     ( this.userProfileName == 'Store User' && this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && failedOutcomes.includes(this.data.assessmentOutcome))    
        // );
    }

    get showReadOnly() {
        return this.approvalScreen == true || this.sfClaimProductActionID != undefined;
    }

   
    get showReturnOptions() {
        return
    }

    get sparePartCanFixForPartMissing() {
        return this.data != undefined &&  ((this.data.shippingMethod != undefined && this.data.shippingMethod != '') || (this.data.replacementItemsFound != undefined && this.data.replacementItemsFound == false));
    }

    get isBrandNewConditionDisabled() {
        if (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') {
            if(
                !(
                    (this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Damaged in Transit' ) ||
                    (this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Returned to Sender' ) ||
                    (this.data.selectedIssueCategory == 'Order Issue' && this.data.selectedOrderIssueSubCategory == 'Product Not As Described' )
                )
            ) {
                return true;
            }
            return false;
        } 
        return this.approvalScreen;
    }

    


    
    //PICKLIST VALUES
    changeOfMindSubCategoryOptions = [
        {
            label: 'Advertised at a lower price',
            value: 'Advertised at a lower price'
        },
        {
            label: 'Product Not As Described',
            value: 'Product Not As Described'
        },
        {
            label: 'Does not suit customer needs',
            value: 'Does not suit customer needs'
        },
        {
            label: 'Other',
            value: 'Other'
        },
        {
            label: 'Not aware Pre Order Date',
            value: 'Not aware Pre Order Date'
        },
    ];

    productIssueProductRecallSubCategoryOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Replacement Item',
            value: 'Replacement Item'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    productIssueDiscontinuedProductlSubCategoryOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Replacement Item(Same Category)',
            value: 'Replacement Item(Same Category)'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    productIssueOutcomeOptions = [
        {
            label: 'Replacement Item',
            value: 'Replacement Item'
        },
        {
            label: 'Replacement Part',
            value: 'Replacement Part'
        },
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back (Major Fault Only)',
            value: 'Money Back'
        }
    ];

    associatedProductOutcomeOptions = [
        // {
        //     label: 'Replacement Item',
        //     value: 'Replacement Item'
        // },
        // {
        //     label: 'Replacement Part',
        //     value: 'Replacement Part'
        // },
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];


    rtsOutcomeOptions = [
        {
            label: 'Replacement Item',
            value: 'Replacement Item'
        },
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    dispatchLateOutcomeOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        },
        {
            label:'Fraud Payment',
            value:'Fraud Payment'
        }
    ];

    incorrectQuantityDispatchOutcomeOptions = [
        {
            label: 'Product Quantity Replaced (Preferred)',
            value: 'Product Quantity Replaced'
        },
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    productIssueProductNonRecallSubCategoryOptions = [
        {
            label: 'Defective/Faulty Product',
            value: 'Defective/Faulty Product'
        },
        {
            label: 'Part Missing',
            value: 'Part Missing'
        }
    ];


    transitAndDeliveryIssueSubCategoryOptionsForWarehouseOnly = [
        {
            "label": "Returned to Sender",
            "value": "Returned to Sender"
        }
    ]

    transitAndDeliveryIssueSubCategoryOptionsForWarehouse = [
        {
            "label": "Product not dispatched",
            "value": "Product not dispatched"
        },
        {
            "label": "Product dispatched late (over 48 hours)",
            "value": "Product dispatched late (over 48 hours)"
        },
        {
            "label": "Damaged in Transit",
            "value": "Damaged in Transit"
        },
        {
            "label": "Lost in Transit",
            "value": "Lost in Transit"
        },
        {
            "label": "Returned to Sender",
            "value": "Returned to Sender"
        },
        {
            "label": "Wrong Product Dispatched/Received",
            "value": "Wrong Product Dispatched/Received"
        },
        {
            "label": "Wrong Product Quantity Dispatched/Received",
            "value": "Wrong Product Quantity Dispatched/Received"
        }
    ]

    transitAndDeliveryIssueSubCategoryOptionsForStore = [
        {
            "label": "Product not dispatched",
            "value": "Product not dispatched"
        },
        {
            "label": "Product dispatched late (over 48 hours)",
            "value": "Product dispatched late (over 48 hours)"
        },
        {
            "label": "Damaged in Transit",
            "value": "Damaged in Transit"
        },
        {
            "label": "Lost in Transit",
            "value": "Lost in Transit"
        },
        {
            "label": "Returned to Sender",
            "value": "Returned to Sender"
        },
        {
            "label": "Wrong Product Dispatched/Received",
            "value": "Wrong Product Dispatched/Received"
        },
        {
            "label": "Wrong Product Quantity Dispatched/Received",
            "value": "Wrong Product Quantity Dispatched/Received"
        }
    ]

    orderIssueSubCategoryOptions = [
        {
            "label": "Duplicate Order",
            "value": "Duplicate Order"
        },
        {
            "label": "Pre-Order Delays",
            "value": "Pre-Order Delays"
        },
        {
            "label": "Orderded Wrong Product",
            "value": "Orderded Wrong Product"
        },
        {
            "label": "Discontinued Product(Sold Out/No Longer Available)",
            "value": "Discontinued Product(Sold Out/No Longer Available)"
        },
        {
            "label": "Cancel Order",
            "value": "Cancel Order"
        },
        {
            label: 'Advertised at a lower price',
            value: 'Advertised at a lower price'
        },
        {
            label: 'Product Not As Described',
            value: 'Product Not As Described'
        }
    ]



    availableSKUsOptions = [
        {
            label: 'AC',
            value: 'AC'
        },
        {
            label: 'Tent',
            value: 'Tent'
        }
    ]

    checkboxOptions = [
        // {
        //     label:'None',
        //     value: undefined
        // },
        {
            label: 'No',
            value: 'No'
        },
        {
            label: 'Yes',
            value: 'Yes'
        }
    ]


    returnOptions = [
        {
            label: 'Wrong Address',
            value: 'Wrong Address'
        },
        {
            label: 'Refused by customer',
            value: 'Refused by customer'
        },
        {
            label: 'Missed Delivery',
            value: 'Missed Delivery'
        },
        {
            label: 'Other',
            value: 'Other'
        }
    ];

    returnOutcomeOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Replacement Item',
            value: 'Replacement Item'
        }
    ];

    wrongProductOutcomeOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Replacement Item',
            value: 'Replacement Item'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    cancelOrderOutcomeOptions = [
        {
            label: 'Store Credit',
            value: 'Store Credit'
        },
        {
            label: 'Money Back',
            value: 'Money Back'
        }
    ];

    // addBackToStockOptions = [
    //     {
    //         label: 'Now',
    //         value: 'Now'
    //     },
    //     {
    //         label: 'Create Return',
    //         value: 'Create Return'
    //     }
    // ];


    @api parentIssueCategoryChange(issueCategory) {
        this.addBackToStockOptions = [{ label: 'Now', value: 'Now' }, { label: 'Create Return', value: 'Create Return' }];
        if (issueCategory == 'Associated Product') {
            this.addBackToStockOptions = [...this.addBackToStockOptions, { label: 'No - Dispose/Strip for Parts', value: 'No - Dispose/Strip for Parts' }];
        }
        //this.isAddbackToStockRequired =  issueCategory == 'Associated Product' ? false : true;
    }


    returnMethodOptions = [
        {
            label: 'Customer Returns',
            value: 'Customer Returns'
        },
        {
            label: 'Prepaid Consignment',
            value: 'Prepaid Consignment'
        },
        {
            label: 'In Store',
            value: 'In Store'
        }
    ]

    dcReturnLocationOptions = [
        { label: 'Brisbane DC', value: 'Brisbane DC' },
        { label: 'Melbourne DC', value: 'Dandenong South DC' },
        { label: 'Perth DC', value: 'Perth DC' },
        { label: 'Sydney DC', value: 'Sydney DC' },
        { label: 'Townsville DC', value: 'Townsville DC' }
    ];

    get addBackToStockLocationOptions() {
        console.log('add back location is calling');
        if (
            (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') ||
            (this.userProfileName == 'Warehouse' && (this.userLocation != undefined && this.userLocation.endsWith("DC"))) 
        ) {
            console.log('add back location is calling if');
            let locations = this.userLocation.split(';');
            let returnArr = [];
            locations.forEach(element => {
                returnArr.push({ label: element.trim(), value: element.trim() });
            });  
            return returnArr;
        }
        else{
           /* return 
            console.log('else is calling');
           /* [
                { label: 'Brendale', value: 'Brendale' },
                { label: 'Brisbane DC', value: 'Brisbane DC' },
                { label: 'Bunbury', value: 'Bunbury' },
                { label: 'Cairns', value: 'Cairns' },
                { label: 'Campbellfield', value: 'Campbellfield' },
                { label: 'Campbelltown', value: 'Campbelltown' },
                { label: 'Canning Vale', value: 'Canning Vale' },
                { label: 'Coffs Harbour', value: 'Coffs Harbour' },
                { label: 'Eastern Creek', value: 'Eastern Creek' },
                { label: 'Fyshwick', value: 'Fyshwick' },
                { label: 'Geraldton', value: 'Geraldton' },
                { label: 'Gosford', value: 'Gosford' },
                { label: 'Kilburn', value: 'Kilburn' },
                { label: 'Mackay', value: 'Mackay' },
                { label: 'Malaga', value: 'Malaga' },
                { label: 'Melbourne DC', value: 'Dandenong South DC' },
                { label: 'Newcastle', value: 'Newcastle' },
                { label: 'Parkinson', value: 'Parkinson' },
                { label: 'Perth DC', value: 'Perth DC' },
                { label: 'Ravenhall', value: 'Ravenhall' },
                { label: 'Rockhampton', value: 'Rockhampton' },
                { label: 'Sunshine Coast', value: 'Sunshine Coast' },
                { label: 'Sydney DC', value: 'Sydney DC' },
                { label: 'Toowoomba', value: 'Toowoomba' },
                { label: 'Townsville DC', value: 'Townsville DC' },
                { label: 'Townsville Store', value: 'Townsville' },
                { label: 'Varsity', value: 'Varsity Lakes' },
                { label: 'Wetherill', value: 'Wetherill Park' },
                { label: 'Ballarat', value: 'Ballarat' },
                { label: 'Bendigo', value: 'Bendigo' },
                { label: 'Mildura', value: 'Mildura' },
                { label: 'Shepparton', value: 'Shepparton' },
                { label: 'Dandenong', value: 'Dandenong' },
                {label: 'Wagga Wagga', value: 'Wagga Wagga'},
                {label: 'Wodonga', value: 'Wodonga'}
            ];*/
            console.log('location in CP'+JSON.stringify(this.locationMap));
            return this.locationMap;
        }
    }
/* @wire(getShippingLocations)
     getShippingLocations({ error, data }) {
         if (data) {
          
             this.locationMap = data.map(location => {
                 const cityNameRes = location.Shipping_Method_API_Name__c.trim();
                 const cleanedCityName = cityNameRes
                     .replace(/^pickup_/, '') 
                     .replace(/_/g, ' ')
                     .split(' ')               
                     .map((word, index) => {
                         if (index === 0) {
                             return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(); 
                         }
                         return word.toLowerCase();
                     })
                     .join(' '); 
                 return {
                     label: cleanedCityName, 
                     value: cleanedCityName 
                 };
             });
            
             this.locationMapRes = data.reduce((map, location) => {
                 const cityNameRes = location.Shipping_Method_API_Name__c.trim(); 
                 const cleanedCityNameRes = cityNameRes
                     .replace(/^pickup_/, '') 
                     .replace(/_/g, ' ')
                     .split(' ')              
                     .map((word, index) => {
                         if (index === 0) {
                             return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                         }
                         return word.toLowerCase(); 
                     })
                     .join(' '); 
                 
                 if (cleanedCityNameRes) {
                     map[cleanedCityNameRes] = cleanedCityNameRes;
                 }
                 return map;
             }, {});
     
             console.log('All Location Map:', JSON.stringify(this.locationMap));
         } else if (error) {
             console.error('Error fetching shipping locations:', error);
         }
     }*/
        @wire(getShippingLocations)
         getShippingLocations({ error, data }) {
             if (data) {
               
                 this.locationMap = data.map(location => ({
                     label: location.Locations__c, 
                     value: location.Locations__c 
                 }));
                 this.locationMapRes = data.reduce((map, location) => {
                     const cityName = location.Locations__c;
                     
                     if (cityName) {  
                         map[cityName] = cityName;
                     } 
                     return map;
                 }, {});
               
                 console.log('All Location Map:', JSON.stringify(this.locationMap));
             } else if (error) {
                 console.error('Error fetching shipping locations:', error);
             } 
     
         }
    get hasProductKnowladgeData() {
        return this.productKnowladgeData != undefined && this.productKnowladgeData.length > 0;
    }

    orderIssueCOMOptions = [
        {
            label: 'COM Process',
            value: 'COM Process'
        },
        {
            label: 'Stop',
            value: 'Stop'
        }
    ];

    get isCOMProcessSelection() {
        return this.data.orderIssueCOMOption != undefined && this.data.orderIssueCOMOption == 'Yes';
    }

    get showUnpackRequestErrorMessage() {
        return this.data != undefined && this.data.isPacked == true && this.data.isShipped == false;
    }

    /*
     * No Parameters.
     * This wire syntax is for the retrieval of the current user's 4WD Location and user data
    */
   @wire(getRecord, { recordId: '$userID', fields: [USER_4WD_LOCATION_FIELD, USER_PROFILE_NAME, USER_NAME] })
    getUserRecordData({ error, data }) {
        if (data) {

            this.userData = data;
            this.userProfileName = getFieldValue(data, USER_PROFILE_NAME);
            this.userLocation = getFieldValue(data, USER_4WD_LOCATION_FIELD);

            //this.userDefaultAddBackToStockLocation = this.getAPICityNameForAddBackToStock(this.userLocation);
            console.log('this.userLocation -> ' + JSON.stringify(this.userLocation, null, 2));
            console.log('this.data.addBackToStockActionLocation -> ' + this.data.addBackToStockActionLocation);

            if(this.data.addBackToStockActionLocation == undefined || this.data.addBackToStockActionLocation == '') {
                this.data.addBackToStockActionLocation = this.userLocation.replace(/_/g, " "); //this.getAPICityNameForAddBackToStock(this.userLocation);
                this.data.userLocation = this.data.addBackToStockActionLocation;
            } else {
                this.data.addBackToStockActionLocation = this.data.addBackToStockActionLocation.replace(/_/g, " ");
            }

            this.userDefaultAddBackToStockLocation = this.userLocation.replace(/_/g, " ");
            console.log('this.userDefaultAddBackToStockLocation -> ' + JSON.stringify(this.userDefaultAddBackToStockLocation, null, 2));
            console.log('976 this.data -> ' + JSON.stringify(this.data, null, 2));
            this.data = JSON.parse(JSON.stringify(this.data));
            this.updateDataObjToParent();
            console.log('this.userProfileName ' + this.userProfileName);
            console.log(this.userProfileName == 'Warehouse');
            console.log('this.userLocation',this.userLocation);
            console.log(this.userLocation != undefined);
            console.log(this.userLocation.endsWith("DC"));
            if(!this.userLocation.includes(';')) {
                if (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') {
                    this.isAddBackToStockLocationChangeDisabled = true;
                } else if (this.userProfileName == 'Warehouse' && (this.userLocation != undefined && this.userLocation.endsWith("DC"))) {
                    this.isAddBackToStockLocationChangeDisabled = true;
                }
                else {
                    this.isAddBackToStockLocationChangeDisabled = false;
                }
            } else {
                this.addBackToStockActionDisabled = false;
            }
        } else if (error) {
            console.log('Error fetching user data ', error);
        }
    }

    @wire(getProductKnowladgeData, { sku: '$data.sku' })
    wiredProductKnowladgeData({ error, data }) {
        if (data) {
            this.productKnowladgeData = data;
            // console.log('this.productKnowladgeData -> ' + JSON.stringify(this.productKnowladgeData, null, 2));

        } else if (error) {
            console.error('Error fetching product knowladge data ids:', error);
        }
    }

    @wire(getProductInfo, { sku: '$data.sku' })
    getProductInfodData({ error, data }) {
        if (data) {
            console.log('this.currentProductCategoryName-',data);
            this.currentProductCategory = data.Product_Category__c;
            this.currentProductCategoryName = data.Product_Category__r.Name;
            if (data.Troubleshooting_FAQ__r != undefined && data.Troubleshooting_FAQ__r.FAQ__c != undefined) {
                this.faqData = data.Troubleshooting_FAQ__r.FAQ__c;
                this.hasFaqData = true;
            } else {
                this.faqData = '';
                this.hasFaqData = false;
            }
        } else if (error) {
            console.error('Error fetching Returned Location picklist values', error);
        }
    }

    /*
    * No Parameters.
    * Retrieval of Shipping Location Records.
   */
    @wire(getShippingLocationList)
    getShippingLocationList({ data, error }) {
        if (data) {

            const arrayToObject1 = (arr, key) => {
                return arr.reduce((obj, item) => {
                    obj[item[key]] = item;
                    return obj;
                }, {})
            };

            this.shippingLocationList = arrayToObject1(data, 'Id');
            //  console.log('this.erpData.shipping_address -> ' + JSON.stringify(this.erpData.shipping_address, null, 2));
            //  let shippingAddress = JSON.parse(JSON.stringify(this.erpData.shipping_address));
            //  let orderShippingCity = shippingAddress.suburb;

            let options = [];
            for (const d of data) {
                if ((this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager')) {
                    if (this.userLocation != undefined && this.userLocation != '') {
                        this.shippingLocationSelected = this.getIdByCity(this.shippingLocationList, this.userLocation);
                        this.locationValue = this.shippingLocationSelected;
                    } else {
                        this.shippingLocationSelected = '';
                    }

                }
                options.push({
                    'label': d.Name,
                    'value': d.Id
                });
            }

            this.shippingLocationOptions = options;

        } else if (error) {

            let formattedError = ldsUtils.reduceErrors(error);
            this.message = formattedError;
            this.displayError = true;

            this.isLoading = false;

        }

    }

    /*
     * No Parameters.
     * Retrieval of Active Couriers.
    */
    @wire(getActiveCouriers)
    getActiveCouriers({ data, error }) {
        if (data) {

            let options = [];
            let newMap = {};

            for (let i = 0; i < data.length; i++) {

                options.push({
                    'label': data[i].Name,
                    'value': data[i].Id
                });

                newMap[data[i].Id] = data[i];

            }

            this.courierOptions = options;
            this.courierMap = newMap;

        } else if (error) {

            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);

        }
    }

    /*
     * No Parameters.
     * Retrieval of Shipping To Address.
    */
    @wire(getShipToAddress)
    getShipToAddress({ data, error }) {

        if (data) {
            this.formatShipToAddress(data);
        } else if (error) {

            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);

        }

    }



    @api validateInput() {
        let valid = [...this.template.querySelectorAll("lightning-input"), ...this.template.querySelectorAll("lightning-combobox")].reduce(
            (validSoFar, input) => {
                if (input.dataset.fieldlabel === "userLocation" && (this.data.addBackToStockActionLocation == undefined || this.data.addBackToStockActionLocation == '')) {
                    input.setCustomValidity("Complete this field.");
                } else {
                    input.setCustomValidity(""); // Clear any custom validation messages
                }

                if(input.dataset.fieldlabel == 'Enter Batch Number' ) {
                    if (this.data.isBatchNumberCorrect == false ) {
                        input.setCustomValidity("Please enter a valid batch number.");
                    } else {
                        input.setCustomValidity(""); // Clear any custom validation messages
                    }
                }

                if (input.checkValidity()) {
                    input.classList.add("slds-has-error");
                    // Scroll to the input element and focus on it
                    input.scrollIntoView();
                    input.focus();
                } else {
                    input.classList.remove("slds-has-error");
                }

                input.reportValidity();
                return validSoFar && input.checkValidity();
            },
            true
        )

        let childValid = true;
        if (this.template.querySelectorAll("c-claim-form-file-upload") != undefined) {
            childValid = [...this.template.querySelectorAll("c-claim-form-file-upload")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let claimFormCpItemProductIssueCchild = true;
        if (this.template.querySelectorAll("c-claim-form-cp-item-product-issue-child") != undefined) {
            claimFormCpItemProductIssueCchild = [...this.template.querySelectorAll("c-claim-form-cp-item-product-issue-child")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let claimFormcpItemProductSelectionValid = true;
        if (this.template.querySelectorAll("c-claim-form-c-p-item-product-selection") != undefined) {
            claimFormcpItemProductSelectionValid = [...this.template.querySelectorAll("c-claim-form-c-p-item-product-selection")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let reusuableLookupValid = true;
        if (this.template.querySelectorAll("c-reusuable-lookup") != undefined) {
            reusuableLookupValid = [...this.template.querySelectorAll("c-reusuable-lookup")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validate();
                },
                true
            )
        }

        let claimFormChangeOfMindValid = true;
        if (this.template.querySelectorAll("c-claim-form-change-of-mind") != undefined) {
            claimFormChangeOfMindValid = [...this.template.querySelectorAll("c-claim-form-change-of-mind")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let claimFormOrderIssueValid = true;
        if (this.template.querySelectorAll("c-claim-form-order-issue") != undefined) {
            claimFormOrderIssueValid = [...this.template.querySelectorAll("c-claim-form-order-issue")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let claimFormOverchargedValid = true;
        if (this.template.querySelectorAll("c-claim-form-overcharged") != undefined) {
            claimFormOverchargedValid = [...this.template.querySelectorAll("c-claim-form-overcharged")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let returOrderValid = true;
        if (this.template.querySelectorAll("c-claimform-return-order") != undefined) {
            returOrderValid = [...this.template.querySelectorAll("c-claimform-return-order")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }


        return valid && claimFormCpItemProductIssueCchild && claimFormcpItemProductSelectionValid && reusuableLookupValid &&
            claimFormChangeOfMindValid && childValid && claimFormOrderIssueValid && claimFormOverchargedValid && returOrderValid;
    }

    connectedCallback() {
        console.log('@@@@this.approvalScreen'+this.approvalScreen);
        setTimeout(() => {
            if (this.approvalScreen == true) {
                this.checkForClaimProductActions();
                this.updateReturnOrderFields();
            }
        }, 100);
        this.fetchUserInformation();
        this.isCommunityUser();

    }
isCommunityUser(){
    console.log('sfCaseID@@@'+this.sfCaseID);
    if (this.sfCaseID) {
        isCreatedByGuestUser({ caseId: this.sfCaseID })
            .then(result => {
                this.isGuest = result;
                console.log('Is created by guest user:', result);
            })
            .catch(error => {
                console.error('Error checking guest user:', error);
            });
    }
}
    fetchUserInformation() {
        getUserInformation({})
            .then(result => {
                console.log('result'+JSON.stringify(result));
                this.userProfileName = result.profileName;
            })
            .catch(error => {
                console.log('error'+JSON.stringify(error));
            });
    }

    getIdByCity(shippingLocationList, city) {
        return Object.values(shippingLocationList).find(location => (location.Name === city || location.Name === '4WD ' + city))?.Id || null;
    }

    @api checkForClaimProductActions() {
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        if (dataLocal.claimProductActions) {
            delete dataLocal.claimProductActions;
        }
        console.log(`***** claimFormCpItems JS dataLocal:${JSON.stringify(dataLocal)}`);
        console.log('isIssueCategoryIsProductIssue@@@'+this.isIssueCategoryIsProductIssue);
        console.log('isIssueCategoryIsProductIssue@@@'+this.isIssueCategoryIsProductIssue);
        console.log('sfClaimProductActionID@@@'+this.sfClaimProductActionID);
        console.log('dataLocal.assesmentPossible@@@'+dataLocal.assesmentPossible);
        dataLocal.isAddbackToStockRequired = false;
        if (this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMAdvertisedAtALowerPrice && this.has48HourPassedSinceShipped != false) {
            dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Change of mind - Advertised at lower price');
        } else if (this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMProductNotAsDescribedORCOMDoesNotSuitCustomerNeeds) {
            if(dataLocal.isShipped == true) {
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    if (this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - not as described/does not suit');
                    }
                    if (this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Change of mind - not as described/does not suit');
                    }
                    // dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - not as described/does not suit');
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Change of mind - not as described/does not suit');
                    dataLocal.isAddbackToStockRequired = true;
                } else if(this.isChangeResolution != true){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Change of mind - not as described/does not suit');
                }
            }
            dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Change of mind - not as described/does not suit');
        } else if (this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMNotAwarePreOrderDate && this.isItemDispatched) {
            if(dataLocal.isShipped == true) {
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    // dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - Pre order date');
                    if (this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - Pre order date');
                    }
                    if (this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Change of mind - Pre order date');
                    }
                    dataLocal.isAddbackToStockRequired = true;
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Change of mind - Pre order date');
                    dataLocal.isAddbackToStockRequired = true;
                } else if(this.isChangeResolution != true){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Change of mind - Pre order date');
                }
            }
            dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Change of mind - Pre order date');
        } else if (this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMOther) {
            if (dataLocal.resolutionOutcome == 'Money Back') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Change of mind - Money Back');
            } else if (dataLocal.resolutionOutcome == 'Store Credit') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Change of mind - Store Credit');
            } else if (dataLocal.resolutionOutcome == 'Replacement Item') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Change of mind - Replacement Item');
            }
            if(dataLocal.isShipped == true) {
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    if(this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - Add back to Stock');
                    }
                    if(this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Change of mind - Add back to Stock');
                    }
                    dataLocal.isAddbackToStockRequired = true;
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Change of mind - Email to call center');
                } else if(this.isChangeResolution != true){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Change of mind - Return Order');
                }
            }
        } else if (this.isIssueCategoryIsProductIssue && dataLocal.readyForApproval && (dataLocal.didCauseDamage != 'Yes' || this.sfClaimProductActionID != undefined)) {
           
            if (dataLocal.resolutionOutcome == 'Replacement Part' && dataLocal.assesmentPossible == 'Now' && dataLocal.didCauseDamage != 'Yes') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order - Part', 'Product Issue - Replacement Part');
            } else if (dataLocal.resolutionOutcome == 'Money Back'  && dataLocal.assesmentPossible == 'Now') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Product Issue - Money Back');
            } else if (dataLocal.resolutionOutcome == 'Store Credit'  && dataLocal.assesmentPossible == 'Now') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Product Issue - Store Credit');
            } else if (dataLocal.resolutionOutcome == 'Replacement Item' && dataLocal.assesmentPossible == 'Now') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Product Issue - Replacement Item');
            }
            if (dataLocal.selectedIssueSubCategory == 'Money Back') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Product Issue - Money Back');
            } else if (dataLocal.selectedIssueSubCategory == 'Store Credit') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Product Issue - Store Credit');
            } else if (dataLocal.selectedIssueSubCategory == 'Replacement Item') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Product Issue - Replacement Item');
            } else if (dataLocal.selectedIssueSubCategory == 'Part Missing' && dataLocal.replacements != undefined && dataLocal.replacements.length > 0) {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order - Part', 'Product Issue - Replacement Part');
            }
            if (this.showCreateDCReturn == true && this.isChangeResolution != true) {
                dataLocal = this.createClaimProductAction(dataLocal, 'Create DC Return', 'Product Issue - Create DC Return');
            }
            if (dataLocal.selectedIssueSubCategory == 'Part Missing'  && dataLocal.replacements.length <= 0 && this.isSpareNotAvailableInTable == true) {
                if(dataLocal.resolutionOutcome == 'Store Credit'){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Product Issue - Store Credit');
                }
                else if(dataLocal.resolutionOutcome == 'Money Back'){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Product Issue - Money Back');
                }
                else if (dataLocal.resolutionOutcome == 'Replacement Item') {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Product Issue - Replacement Item');
                }
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    if (this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Product Issue - Parts Missing');
                    }
                    if (this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Product Issue - Parts Missing');
                    }
                    // dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - not as described/does not suit');
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Product Issue - Parts Missing');
                    dataLocal.isAddbackToStockRequired = true;
                } else if(this.isChangeResolution != true){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Product Issue - Parts Missing');
                }
                
            }
            if (dataLocal.claimProductFinalOutcome == 'Add Back To Stock (Seconds)' && this.isChangeResolution != true) {
                dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Product Issue');
                dataLocal.isAddbackToStockRequired = true;
            } else if (dataLocal.claimProductFinalOutcome == 'Add Back To Stock (Parts)' && this.isChangeResolution != true) {
                dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Parts', 'Product Issue');
                dataLocal.isAddbackToStockRequired = true;
            }


        } else if (this.isIssueCategoryIsTransitAndDeliveryIssue) {
            console.log('inside transit and delivery issue');
            console.log('dataLocal.resolutionOutcome@@@@@@@@'+dataLocal.resolutionOutcome);
            if (dataLocal.resolutionOutcome == 'Gift Card' || dataLocal.resolutionOutcome == 'Store Credit') {
                if (this.isIssueCategoryIsFreightRefund) {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Freight Refund');
                } else { 
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Delivery and Transit Issue');
                }
            } else if (dataLocal.resolutionOutcome == 'Money Back') {
                if (this.isIssueCategoryIsFreightRefund) {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Freight Refund');
                } else {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Delivery and Transit Issue');
                }
               
                }
                else if(dataLocal.resolutionOutcome == 'Fraud Payment'){
                    console.log('coming inside fraud payment');
                    if (this.isIssueCategoryIsFreightRefund) {
                        const quantity = this.cardData[0].qty;
                        console.log('quantity###'+quantity);
                        console.log('this.cardData.qty 777'+this.cardData[0].qty);
                        //dataLocal = this.createClaimProductAction(dataLocal, 'Fraud Payment', 'Order Issue - Cancel Order - Fraud');
                       for (let i = 0; i < quantity; i++) {
                            dataLocal = this.createClaimProductAction(
                                dataLocal,
                                'Fraud Payment',
                                'Freight Refund'
                            );
                        }
                        console.log('dataLocal@@@@'+JSON.stringify(dataLocal));
                    }

            } else if (dataLocal.resolutionOutcome == 'Product Quantity Replaced') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Product Quantity Replaced', 'Delivery and Transit Issue');
            } else if (dataLocal.resolutionOutcome == 'Replacement Item') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Delivery and Transit Issue - Replacement Item');
            } else if (dataLocal.resolutionOutcome == 'Replacement Part') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order - Part', 'Delivery and Transit Issue - Replacement Part');
            }

            if (this.TDIReturnedtoSender) {
                if ((this.sfClaimProductActionID == undefined || this.isGuest) && this.sfCaseID == undefined) {
                    if(dataLocal.isShipped == true) {
                        if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                            if (this.isBrandNewConditionIsYes) {
                                dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Delivery and Transit Issue');
                            }
                            if (this.isBrandNewConditionIsNo) {
                                dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Delivery and Transit Issue');
                            }
                            dataLocal.isAddbackToStockRequired = true;
                            dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Delivery and Transit Issue');
                        } else if(this.isChangeResolution != true){
                            dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Delivery and Transit Issue');
                        }
                    }

                    // dataLocal = this.createClaimProductAction(dataLocal, 'Warehouse Outcome for RTS', 'Delivery and Transit Issue');
                }


            }
            if (this.TDIWrongProductDispatched) {
                if(dataLocal.isShipped == true) {
                    if(dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                        if (this.isBrandNewConditionIsYes) {
                            dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Delivery and Transit Issue');
                        }
                        if (this.isBrandNewConditionIsNo) {
                            dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Delivery and Transit Issue');
                        }
                        dataLocal.isAddbackToStockRequired = true;
                        dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Delivery and Transit Issue');
                    } else if(this.isChangeResolution != true){
                        dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Order Issue');
                    }
                }
            }
            if (this.TDIDamagedinTransit) {
                if(dataLocal.isShipped == true) {
                    if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                        if(this.isBrandNewConditionIsYes) {
                            dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Change of mind - Add back to Stock');
                        }
                        if(this.isBrandNewConditionIsNo) {
                            dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Change of mind - Add back to Stock');
                        }
                        dataLocal.isAddbackToStockRequired = true;
                        dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Change of mind - Email to call center');
                    } else if(this.isChangeResolution != true){
                        dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Change of mind - Return Order');
                    }
                }
                
                // if (dataLocal.warrantyClaimDCDisposalCompulsory == true) {
                //     dataLocal = this.createClaimProductAction(dataLocal, 'Create DC Return', 'Delivery Issue - Create DC Return');
                // } else {
                //     dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Delivery and Transit Issue');
                //     dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Delivery and Transit Issue');
                // }
            }
        } else if (this.isIssueCategoryIsOrderIssue) {
            if (dataLocal.resolutionOutcome == 'Gift Card' || dataLocal.resolutionOutcome == 'Store Credit') {
                if (this.data.selectedOrderIssueSubCategory == 'Advertised at a lower price') {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Order Issue - Advertised at a lower price');
                } else if(this.isCOMProcessSelection == true && (dataLocal.resolutionOutcome == 'Gift Card' || dataLocal.resolutionOutcome == 'Store Credit') ) {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Order Issue - Change of mind');
                } else {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Order Issue');
                }
            } else if (dataLocal.resolutionOutcome == 'Money Back') {
                if (this.data.selectedOrderIssueSubCategory == 'Advertised at a lower price') {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Order Issue - Advertised at a lower price');
                } else {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Order Issue');
                }
            } else if (dataLocal.resolutionOutcome == 'Product Quantity Replaced') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Product Quantity Replaced', 'Order Issue');
            } else if (dataLocal.resolutionOutcome == 'Replacement Item') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Order Issue - Replacement Item');
            }
            else if (dataLocal.resolutionOutcome == 'Fraud Payment') {
               /* const quantity = this.cardData.map(item => item.qty);
                console.log('All Quantities:', quantity);
                
               /* this.cardData.forEach((item, index) => {
                    console.log(`Item ${index} qty: ${item.qty}`);
                  });*/
                  if (Array.isArray(this.cardData)) {
                    const quantity1 = this.cardData.map(item => item.qty);
                    console.log('All Quantities:', quantity1);
                  }
                console.log('this.cardData for looping'+JSON.stringify(this.erpData));
                const prettyJson = JSON.stringify(this.cardData, null, 2);
                console.log('this.cardData.qty####'+prettyJson);
                console.log('this.cardData.qty@@@@@'+this.cardData[0].qty);
                //const quantity = this.cardData[0].qty;
                //dataLocal = this.createClaimProductAction(dataLocal, 'Fraud Payment', 'Order Issue - Cancel Order - Fraud');
               // for (let i = 0; i < quantity; i++) {
                    dataLocal = this.createClaimProductAction(
                        dataLocal,
                        'Fraud Payment',
                        'Order Issue'
                    );
                //}

                /*    this.erpData.order_items.forEach(item => {
                        // Use main item's qty
                        const mainQty = item.qty || 0;
                        for (let i = 0; i < mainQty; i++) {
                          dataLocal = this.createClaimProductAction(dataLocal, 'Fraud Payment', 'Order Issue');
                        }
                      
                        // If there are child items, use their qtys too
                        if (Array.isArray(item.child_items) && item.child_items.length > 0) {
                          item.child_items.forEach(child => {
                            const childQty = child.qty || 0;
                            for (let i = 0; i < childQty; i++) {
                              dataLocal = this.createClaimProductAction(dataLocal, 'Fraud Payment', 'Order Issue');
                            }
                          });
                        }
                      });*/
               
            }
             
            

            if(dataLocal.isShipped == true) {
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    if (this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Order Issue');
                    }
                    if (this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Order Issue');
                    }
                    dataLocal.isAddbackToStockRequired = true;
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Order Issue');
                } else if (this.data.selectedOrderIssueSubCategory != 'Overcharged' && this.data.selectedOrderIssueSubCategory != 'Advertised at a lower price' && dataLocal.addBackToStockAction == 'Create Return' && this.isChangeResolution != true) {
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Order Issue');
                }
            }

        } else if (this.isIssueCategoryIsAssociatedProduct) {
            if (dataLocal.resolutionOutcome == 'Replacement Part') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order - Part', 'Associated Product');
            } else if (dataLocal.resolutionOutcome == 'Money Back') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Money Back', 'Associated Product');
            } else if (dataLocal.resolutionOutcome == 'Store Credit') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card', 'Associated Product');
            } else if (dataLocal.resolutionOutcome == 'Replacement Item') {
                dataLocal = this.createClaimProductAction(dataLocal, 'Warranty Order', 'Associated Product');
            }

            if(dataLocal.isShipped == true) {
                if (dataLocal.addBackToStockAction == 'Now' && this.isChangeResolution != true) {
                    if (this.isBrandNewConditionIsYes) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock', 'Associated Product - Add back to Stock');
                    }
                    if (this.isBrandNewConditionIsNo) {
                        dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock - Seconds', 'Associated Product - Add back to Stock');
                    }
                    dataLocal.isAddbackToStockRequired = true;
                    dataLocal = this.createClaimProductAction(dataLocal, 'Email to call center', 'Associated Product - Add back to Stock');
                } else if(this.isChangeResolution != true){
                    dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Associated Product - Add back to Stock');
                }
            }
        } 
        let hasDraft = false;
        if(dataLocal && dataLocal.assessmentTableData && dataLocal.assessmentTableData.length > 0) {
            for(let temp of dataLocal.assessmentTableData) {
                if(temp.Assessment_Outcome__c == 'Draft') {
                    hasDraft =  true;
                }
            }
        }
        if (this.isIssueCategoryIsProductIssue && (dataLocal.assesmentPossible == 'Later' || hasDraft) && (this.sfClaimProductActionID == undefined || this.isGuest)) {
            dataLocal = this.createClaimProductAction(dataLocal, 'Assessment form', 'Product Issue - Assessment Pending');
            console.log('dataLocal@@@123'+JSON.stringify(dataLocal));
        }
        if(dataLocal.productReturnAction != undefined && dataLocal.productReturnAction == 'Yes' && (this.sfClaimProductActionID == undefined || this.isGuest) && this.isChangeResolution != true) {
            dataLocal = this.createClaimProductAction(dataLocal, 'Return Order', 'Product Issue - Return Order');
        }

        if (dataLocal.resolutionOutcome == 'Replacement Item' || dataLocal.resolutionOutcome == 'Replacement Part' || dataLocal.resolutionOutcome == 'Replacement Item(Same Category)' ||
            dataLocal.selectedIssueSubCategory == 'Part Missing' && this.isChangeResolution != true) {
            let returnString = '';
            let foundLowStockItems = false;

            if (dataLocal.replacements != undefined && dataLocal.replacements.length > 0 && this.isChangeResolution != true) {
                returnString += 'Dear Service Team,\n\n';
                returnString += 'Please be advised that the following SKUs are out of stock at ' + dataLocal.shippingLocationName + ' and need to be refilled:\n\n';

                for (let index = 0; index < dataLocal.replacements.length; index++) {
                    const replacementsElement = dataLocal.replacements[index];
                    if (replacementsElement.qty == 0 && replacementsElement.qtyToOrder > 0) {
                        returnString += 'SKU: ' + replacementsElement.sku + '\n';
                        foundLowStockItems = true;
                    }
                }
                returnString += '\nThanks';
            }

            if (foundLowStockItems) {
                dataLocal = this.createClaimProductActionForLowStock(dataLocal, '', 'Email for Stock', returnString);
            }
        }
        this.data = JSON.parse(JSON.stringify(dataLocal));

        this.updateDataObjToParent();
    }

    getAPICityNameForAddBackToStock(location) {
        /*const locationMap = {
            'Brendale': 'Brendale',
            'Brisbane DC': 'Brisbane DC',
            'Bunbury': 'Bunbury',
            'Cairns': 'Cairns',
            'Campbellfield': 'Campbellfield',
            'Campbelltown': 'Campbelltown',
            'Canning Vale': 'Canning Vale',
            'Coffs Harbour': 'Coffs Harbour',
            'Eastern Creek': 'Eastern Creek',
            'Fyshwick': 'Fyshwick',
            'Geraldton': 'Geraldton',
            'Gosford': 'Gosford',
            'Kilburn': 'Kilburn',
            'Mackay': 'Mackay',
            'Malaga': 'Malaga',
            'Melbourne DC': 'Dandenong South DC',
            'Newcastle': 'Newcastle',
            'Parkinson': 'Parkinson',
            'Perth DC': 'Perth DC',
            'Ravenhall': 'Ravenhall',
            'Rockhampton': 'Rockhampton',
            'Sunshine Coast': 'Sunshine Coast',
            'Sydney DC': 'Sydney DC',
            'Toowoomba': 'Toowoomba',
            'Townsville DC': 'Townsville DC',
            'Townsville Store': 'Townsville',
            'Varsity': 'Varsity Lakes',
            'Wetherill': 'Wetherill Park',
            'Ballarat': 'Ballarat',
            'Bendigo': 'Bendigo',
            'Mildura': 'Mildura',
            'Shepparton': 'Shepparton',
            'Dandenong': 'Dandenong',
            'Wagga Wagga': 'Wagga Wagga',
            'Wodonga': 'Wodonga'
        };*/
        const locationMap = this.locationMapRes;
        console.log('locationMap'+JSON.stringify(locationMap));
        
                const normalizedLocation = location.trim();
                console.log('normalizedLocation'+normalizedLocation);
                console.log('normalizedLocation@@'+locationMap[normalizedLocation]);
                return locationMap[normalizedLocation];

    }

    updateReturnOrderFields() {
        if (this.erpData != null && this.data != null) {
            this.data.refundType = this.erpData.refund_type != undefined ? this.erpData.refund_type == 'manual' ? 'Manual' : this.erpData.refund_type : 'Manual';
            this.data.caseType = this.erpData.store_name == 'WHOLESALE ACCOUNTS' ? 'Commercial' :
                this.erpData.store_name == '4WD SUPACENTRE (CAMPER TRAILERS)' || this.erpData.order_type == 'Camper Trailers' ?
                    'Camper Trailers' : 'Claims Department';
            this.data.salesChannel = this.erpData.sales_channel ? this.erpData.sales_channel : '';
            this.data.refundMethod = this.data.caseType == 'Commercial' ? 'Invoice Credit' :
                this.data.refundType == 'integration' || this.data.salesChannel == 'Camper_Trailers' ? 'Integration' : 'Bank Account';

            // this.updateDataObjToParent();
        }
    }


    createClaimProductAction(dataLocal, actionType, source) {
        //Check for exisitng claim product action
        //if(actionType != 'Fraud Payment'){

        
        let claimProductAction = {};
        let indexOfCPA = 0;

        if (dataLocal.claimProductActions != undefined && dataLocal.claimProductActions != []) {
            indexOfCPA = dataLocal.claimProductActions.findIndex(record => record['Action_Type__c'] === actionType);
            if (indexOfCPA != -1) {
                claimProductAction = dataLocal.claimProductActions[indexOfCPA];
            } else {
                indexOfCPA = dataLocal.claimProductActions.length;
            }
        } else {
            dataLocal.claimProductActions = [];
        }

        let dataLocalTempCopy = JSON.parse(JSON.stringify(dataLocal));

        claimProductAction.Action_Type__c = actionType;
        claimProductAction.Status__c = 'Pending';
        claimProductAction.JSON__c = dataLocalTempCopy;
        claimProductAction.Description__c = this.generateClaimProductActionDescritption(dataLocalTempCopy, actionType, source);
        claimProductAction.CP_Action_Amount__c = this.generateClaimProductActionAmount(dataLocalTempCopy, actionType, source);

        dataLocal.claimProductActions[indexOfCPA] = claimProductAction;
        return dataLocal;
    /*}
    else{
        if (!dataLocal.claimProductActions) {
            dataLocal.claimProductActions = [];
        }
    
        let dataLocalTempCopy = JSON.parse(JSON.stringify(dataLocal));
    
        let claimProductAction = {
            Action_Type__c: actionType,
            Status__c: 'Pending',
            JSON__c: dataLocalTempCopy,
            Description__c: this.generateClaimProductActionDescritption(dataLocalTempCopy, actionType, source),
            CP_Action_Amount__c: this.generateClaimProductActionAmount(dataLocalTempCopy, actionType, source),
            Unique_ID__c: `${Date.now()}-${Math.random()}` // optional uniqueness
        };
    
        dataLocal.claimProductActions.push(claimProductAction);
    
        return dataLocal;
    }*/
    }

    createClaimProductActionForLowStock(dataLocal, actionType, source, description) {
        //Check for exisitng claim product action
        let claimProductAction = {};
        let indexOfCPA = 0;

        if (dataLocal.claimProductActions != undefined && dataLocal.claimProductActions != []) {
            indexOfCPA = dataLocal.claimProductActions.findIndex(record => record['Action_Type__c'] === actionType);
            if (indexOfCPA != -1) {
                claimProductAction = dataLocal.claimProductActions[indexOfCPA];
            } else {
                indexOfCPA = dataLocal.claimProductActions.length;
            }
        } else {
            dataLocal.claimProductActions = [];
        }

        let dataLocalTempCopy = JSON.parse(JSON.stringify(dataLocal));

        claimProductAction.Action_Type__c = 'Email for Stock';
        claimProductAction.Status__c = 'Pending';
        claimProductAction.JSON__c = dataLocalTempCopy;
        claimProductAction.Description__c = 'Email alert out of stock for part';
        claimProductAction.CP_Action_Amount__c = this.generateClaimProductActionAmount(dataLocalTempCopy, actionType, source);

        dataLocal.claimProductActions[indexOfCPA] = claimProductAction;

        return dataLocal;
    }

    generateClaimProductActionAmount(dataLocalTempCopy, actionType, source) {

        if (actionType == 'Gift Card' || actionType == 'Store Credit') {
            if (source == 'Order Issue - Advertised at a lower price') {
                return dataLocalTempCopy.creditDueAmount;
            } else if (source.includes('Change of mind')) {
                return (dataLocalTempCopy.itemPrice - this.restockFee);
            } else if (source == 'Freight Refund') {
                return dataLocalTempCopy.freightRefunded;
            } else {
                return dataLocalTempCopy.itemPrice;
            }
        }
        if (actionType == 'Money Back') {
            if (source == 'Order Issue - Advertised at a lower price') {
                return dataLocalTempCopy.creditDueAmount;
            } else if (source == 'Freight Refund') {
                return dataLocalTempCopy.freightRefunded;
            } else {
                return dataLocalTempCopy.itemPrice;
            }
        }
        if (actionType == 'Fraud Payment') {
            if (source == 'Order Issue - Advertised at a lower price') {
                return dataLocalTempCopy.creditDueAmount;
            } else if (source == 'Freight Refund') {
                return dataLocalTempCopy.freightRefunded;
            } else {
                return dataLocalTempCopy.itemPrice;
            }
        }
        return undefined;
    }

    generateClaimProductActionDescritption(dataLocalTempCopy, actionType, source) {

        if (actionType == 'Gift Card' || actionType == 'Store Credit') {
            if (source == 'Order Issue - Advertised at a lower price') {
                return 'Credit of $' + dataLocalTempCopy.creditDueAmount;
            } else if (actionType == 'Gift Card' && source.includes('Change of mind')) {
                let calculatedItemPrice = dataLocalTempCopy.itemPrice - this.restockFee;
                return 'Credit of $' + Math.round(calculatedItemPrice * 100) / 100;
            } else if (source == 'Freight Refund') {
                return 'Credit of $' +dataLocalTempCopy.freightRefunded;
            } else {
                return 'Credit of $' + dataLocalTempCopy.itemPrice;
            }
        }
        if (actionType == 'Add Back to Stock' || actionType == 'Add Back to Stock - Seconds') {
            return 'SKU ' + dataLocalTempCopy.sku;
        }
        if (actionType == 'Warranty Order - Part') {
            let returnString = '';
            if (dataLocalTempCopy.replacements != undefined && dataLocalTempCopy.replacements.length > 0) {
                for (let index = 0; index < dataLocalTempCopy.replacements.length; index++) {
                    const replacementsElement = dataLocalTempCopy.replacements[index];
                    returnString += replacementsElement.qtyToOrder + 'x ' + replacementsElement.sku + ', ' + dataLocalTempCopy.shippingMethod + ' to ' + dataLocalTempCopy.shippingLocationName + '\n ';
                }
            }
            return returnString;
        }
        if (actionType == 'Money Back') {
            if (source == 'Order Issue - Advertised at a lower price') {
                return 'Value of $' + dataLocalTempCopy.creditDueAmount + ' via Bank Account';
            } else if (source == 'Freight Refund') {
                return 'Value of $' + dataLocalTempCopy.freightRefunded + ' via Bank Account';
            } else {
                return 'Value of $' + dataLocalTempCopy.itemPrice + ' via Bank Account';
            }
        }

        if (actionType == 'Warranty Order') {
            return '1x ' + dataLocalTempCopy.sku + ', ' + dataLocalTempCopy.shippingMethod + ' to ' + dataLocalTempCopy.shippingLocationName + '\n ';
        }

        if (actionType == 'Email to call center') {
            return 'Email to call center';
        }
        if (actionType == 'Email to call center') {
            return 'Email for Stock';
        }

        if (actionType == 'Create DC Return') {
            return '1x ' + dataLocalTempCopy.sku + ' to ' + dataLocalTempCopy.returnDCLocation + '\n ';
        }

        if (actionType == 'Assessment form') {
            return 'Complete the Assessment Form';
        }

    }

    /*
     * Parameters
        * event - onchange event from the lightning input and lightning combobox components.
     * Assignment of serialBatchValue, selectedResolution, and isValid properties on the cardData variable.
    */

    handleIssueCategoryChange(event) {
        let id = event.target.dataset.id.substring(0, event.target.dataset.id.indexOf(';'));
        console.log(JSON.stringify(this.data));
        console.log(JSON.stringify(this.cardData));
        if (event.target.dataset.fieldlabel) {
            let dataLocal = JSON.parse(JSON.stringify(this.data));
            if (event.target.dataset.fieldlabel == 'selectedIssueCategory') {
                dataLocal['selectedIssueSubCategory'] = ''
                dataLocal['selectedOrderIssueSubCategory'] = '';
                dataLocal['readyForApproval'] = true;
                dataLocal['assesmentPossibleDisabled'] = false;
                dataLocal['disableCustomerOutcome'] = false;
                dataLocal['hasApprovalStatus'] = false;
                dataLocal['isPendingAssessment'] = false;
                dataLocal['addBackToStockAction'] = '';
                dataLocal['shippingMethod'] = ''; 
                dataLocal['uploadedFiles'] = [];
                dataLocal['addBackToStockActionDisabled'] = false;
            }
            dataLocal[event.target.dataset.fieldlabel] = event.target.value;

            this.data = JSON.parse(JSON.stringify(dataLocal));
            this.updateDataObjToParent();
        }
    }

    handleChange(event) {

        let id = event.target.dataset.id.substring(0, event.target.dataset.id.indexOf(';'));
        let type = event.target.dataset.type;
        let dataLocal = JSON.parse(JSON.stringify(this.data));

        if (type == 'input') {
            if (event.target.dataset.fieldlabel == 'Enter Serial Number') {
                dataLocal.serialValue = event.target.value;
            } else if (event.target.dataset.fieldlabel == 'Enter Batch Number') {
                dataLocal.batchValue = event.target.value;

                clearTimeout(this.batchTypingTimer);
                
                // Call Apex method to validate batch number
                
                if(dataLocal.batchValue == '' || dataLocal.batchValue == undefined) {
                    dataLocal.isBatchNumberCorrect = true;
                    let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
                    inputField.setCustomValidity('');
                    dataLocal.isBatchNumberCorrect = true;
                } else {
                    this.batchTypingTimer = setTimeout(() => {
                        isValidBatchNumberApex({ batchNumber: this.data.batchValue+'', skuString: dataLocal.sku })
                        .then(result => {
                            let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
                            
                            if (!result) {
                                inputField.setCustomValidity('Please enter a valid batch number.');
                                dataLocal.isBatchNumberCorrect = false;
                            } else {
                                inputField.setCustomValidity('');
                                dataLocal.isBatchNumberCorrect = true;
                            }

                            inputField.reportValidity();
                            this.data = JSON.parse(JSON.stringify(dataLocal));
                            this.updateDataObjToParent();
                            
                        })
                        .catch(error => {
                            console.error('Error calling Apex method isValidBatchNumberApex:', error);
                        });
                    }, this.doneTypingInterval);
                }
                
                
                
            } else {
                dataLocal[event.target.dataset.fieldlabel] = event.target.value;
            }
            let fieldLabel = event.target.dataset.fieldlabel;
            if (fieldLabel == 'itemPrice' || fieldLabel == 'priceAdvertised') {
                if (dataLocal['itemPrice'] != undefined && dataLocal['priceAdvertised'] != undefined) {
                    let priceAdvertised = parseFloat(dataLocal['priceAdvertised']);
                    if (isNaN(priceAdvertised) || priceAdvertised === 0) {
                        dataLocal['creditDueAmount'] = 0;
                    } else {
                        const inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${event.target.dataset.fieldlabel}"]`);
                        if (priceAdvertised >= dataLocal['itemPrice']) {
                            inputField.setCustomValidity('Price Advertised must be lesser than Price Paid.');
                        } else {
                            inputField.setCustomValidity('');
                        }
                        inputField.reportValidity();
                        // Calculate credit due amount
                        let creditDueAmount = dataLocal['itemPrice'] - priceAdvertised;
                        // Restrict to 2 decimal places
                        creditDueAmount = parseFloat(creditDueAmount.toFixed(2));
                        dataLocal['creditDueAmount'] = creditDueAmount;
                    }
                } else {
                    dataLocal['creditDueAmount'];
                }
            }
            if ( fieldLabel == 'freightRefunded') {
                let freightRefunded = parseFloat(dataLocal['freightRefunded']);
                if (isNaN(freightRefunded) || freightRefunded === 0) {
                    dataLocal['creditDueAmount'] = 0;
                } else {
                    const inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${event.target.dataset.fieldlabel}"]`);
                    if (freightRefunded > dataLocal['itemPrice']) {
                        inputField.setCustomValidity('Freight Refunded cannot be greater than Freight Paid.');
                    } else {
                        inputField.setCustomValidity('');
                    }
                    inputField.reportValidity();
                   
                }
            }

            if (fieldLabel == 'CP_Quantity_Dispatched__c') {
                dataLocal['CP_Quantity_Difference__c'] = 1 - event.target.value;
            }
            if(fieldLabel == dataLocal.batchLabel) {
                if(dataLocal.batchValue != '' && dataLocal.batchValue != undefined) {
                    this.validateBatchNumber(dataLocal.batchValue, dataLocal);
                }
            }
        } else if (type == 'combobox') {
            if (event.target.dataset.fieldlabel) {
                dataLocal[event.target.dataset.fieldlabel] = event.target.value;
                if (event.target.dataset.fieldlabel == 'selectedIssueCategory') {
                    dataLocal['selectedIssueSubCategory'] = ''
                    dataLocal['selectedOrderIssueSubCategory'] = '';
                    dataLocal['readyForApproval'] = true;
                    dataLocal['assesmentPossibleDisabled'] = false;
                    dataLocal['disableCustomerOutcome'] = false;
                    dataLocal['hasApprovalStatus'] = false;
                    dataLocal['isPendingAssessment'] = false;
                    dataLocal['addBackToStockAction'] = '';
                    dataLocal['shippingMethod'] = ''; 
                    dataLocal['uploadedFiles'] = [];
                    dataLocal['addBackToStockActionDisabled'] = false;
                }
                if (event.target.dataset.fieldlabel == 'selectedIssueSubCategory' || event.target.dataset.fieldLabel == 'selectedOrderIssueSubCategory') {
                    dataLocal['readyForApproval'] = true;
                    dataLocal['assesmentPossibleDisabled'] = false;
                    dataLocal['disableCustomerOutcome'] = false;
                    dataLocal['hasApprovalStatus'] = false;
                    dataLocal['isPendingAssessment'] = false;
                    dataLocal['addBackToStockAction'] = '';
                    dataLocal['shippingMethod'] = ''; 
                    dataLocal['uploadedFiles'] = [];
                }

              
                if(event.target.dataset.fieldlabel == 'didCauseDamage') {
                    if(event.target.value == 'Yes') {
                        let addressObj = this.filterShipToAddressByLabel('4WD Brendale');
                        dataLocal['shipToAddress'] = addressObj;
                        dataLocal['productReturnAction'] = 'Yes';
                        dataLocal['shippingTo'] = 'X4WD_Brendale';
                        dataLocal['shippingToName']  = '4WD Brendale';
                    } else {
                        // dataLocal['shipToAddress'] = {};
                        dataLocal['productReturnAction'] = 'No';
                        dataLocal['shippingTo'] = '';
                        dataLocal['shippingToName']  = '';
                    }
                }

                // if (event.target.dataset.fieldlabel == 'selectedIssueCategory' || event.target.dataset.fieldlabel == 'selectedIssueSubCategory' || event.target.dataset.fieldLabel == 'selectedOrderIssueSubCategory') {
                //     dataLocal['uploadedFiles'] = [];
                // }

                if (event.target.dataset.fieldlabel == 'isIssueResolved') {
                    if (event.target.value == 'Yes') {
                        dataLocal['readyForApproval'] = false;
                    } else {
                        dataLocal['readyForApproval'] = true;
                    }
                }

                if (event.target.dataset.fieldlabel == 'selectedIssueSubCategory' && dataLocal.selectedIssueCategory == '30 Day Change of Mind') {
                    if (event.target.value == 'Advertised at a lower price' && this.has48HourPassedSinceShipped == true) {
                        dataLocal['readyForApproval'] = false;
                    } else if (event.target.value == 'Advertised at a lower price' && this.has48HourPassedSinceShipped == false) {
                        dataLocal['readyForApproval'] = true;
                    }

                }

                // if(event.target.dataset.fieldlabel == 'selectedOrderIssueSubCategory' ) {
                //     if(this.isItemDispatched == true) {
                //         // dataLocal['Approval_Status__c'] = 'Rejected';
                //         // dataLocal['Status__c'] = 'Closed';

                //         dataLocal['readyForApproval'] = false;
                //     } else {
                //         dataLocal['readyForApproval'] = true;
                //     }

                // }

                if (event.target.dataset.fieldlabel == 'selectedIssueSubCategory') {
                    
                    dataLocal['resolutionOutcome'] = '';
                    dataLocal['replacements'] = [];
                    dataLocal['assesmentPossibleDisabled'] = false;
                    dataLocal['disableCustomerOutcome'] = false;

                    if(event.detail.value == 'Part Missing')  {
                        dataLocal['resolutionOutcome'] = 'Warranty Order - Part';
                    }

                    if(event.detail.value == 'Returned to Sender') {
                        if (this.userProfileName == 'Warehouse') {
                            dataLocal['addBackToStockAction'] = 'Now';
                            dataLocal['addBackToStockActionLocation'] = dataLocal['addBackToStockActionLocation'] != undefined ? dataLocal['addBackToStockActionLocation'] : this.userDefaultAddBackToStockLocation;
                            if(dataLocal['userLocation'] == undefined) {
                                dataLocal['userLocation'] = dataLocal['addBackToStockActionLocation'];
                            }
                            dataLocal['userLocationName'] = dataLocal['addBackToStockActionLocation']; //this.shippingToOptions.find(opt => opt.value === this.data.addBackToStockActionLocation).label;
                        }
                    }
                    
                    // if(event.detail.value == 'Damaged in Transit') {
                    //     if(this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') {
                    //         dataLocal['addBackToStockAction'] = 'Now';
                    //         dataLocal['addBackToStockActionDisabled'] = true;
                    //         dataLocal.addBackToStockActionLocation = dataLocal.addBackToStockActionLocation != undefined ? dataLocal.addBackToStockActionLocation : this.userDefaultAddBackToStockLocation;
                    //         dataLocal.userLocation = dataLocal.userLocation != undefined ? dataLocal.userLocation : this.userLocation;
                    //     } else if(this.userProfileName == 'Chat Agent' || this.userProfileName == 'Claim Manager' || this.userProfileName == 'Claims Agent' || this.userProfileName == 'Claim User') {
                    //         dataLocal['addBackToStockAction'] = 'Create Return';
                    //         dataLocal['addBackToStockActionDisabled'] = true;
                    //     } else {
                    //         dataLocal['addBackToStockActionDisabled'] = false;
                    //     }
                    // } else {

                    // }

                    

                }

                if (event.target.dataset.fieldlabel == 'selectedOrderIssueSubCategory') {
                    dataLocal['resolutionOutcome'] = '';
                    if (event.target.value == 'Pre-Order Delays') {
                        dataLocal['resolutionOutcome'] = 'Store Credit';
                    }
                }

                if (event.target.dataset.fieldlabel == 'shippingTo') {
                    this.data.shippingToName = event.target.options.find(opt => opt.value === event.detail.value).label;
                    dataLocal = this.filterShipToAddress(event.detail.value);
                }
                if (event.target.dataset.fieldlabel == 'Courier') {
                    dataLocal.courierName = event.target.options.find(opt => opt.value === event.detail.value).label;
                }

                if (event.target.dataset.fieldlabel == 'proceedWithCancelOrder') {
                    if (event.detail.value == 'Yes') {
                        dataLocal.resolutionOutcome = 'Money Back';
                    } else {
                        dataLocal.resolutionOutcome == '';
                    }
                }

                if (event.target.dataset.fieldlabel == 'addBackToStockAction') {
                    dataLocal.addBackToStockActionLocation = dataLocal.addBackToStockActionLocation != undefined ? dataLocal.addBackToStockActionLocation : this.userDefaultAddBackToStockLocation;

                    if ( 
                        (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager' ) &&
                        !(
                            (dataLocal.selectedIssueCategory == 'Transit/Delivery Issue' && dataLocal.selectedIssueSubCategory == 'Damaged in Transit' ) ||
                            (dataLocal.selectedIssueCategory == 'Transit/Delivery Issue' && dataLocal.selectedIssueSubCategory == 'Returned to Sender' ) ||
                            (dataLocal.selectedIssueCategory == 'Order Issue' && dataLocal.selectedOrderIssueSubCategory == 'Product Not As Described' )
                        )
                        
                    ) {
                        dataLocal.isBrandNewCondition = 'Yes';
                    } 
                    if(dataLocal['userLocation'] == undefined) {
                        dataLocal['userLocation'] = dataLocal['addBackToStockActionLocation'];
                    }
                    // dataLocal.userLocation = dataLocal.userLocation != undefined ? dataLocal.userLocation : this.userLocation;
                }

                if (event.target.dataset.fieldlabel == 'userLocation') {
                    console.log('event.detail.value -> ' + JSON.stringify(event.detail.value, null, 2));
                    dataLocal.userLocation = event.detail.value;
                    dataLocal.userLocationName = event.target.options.find(opt => opt.value === event.detail.value).label;
                    dataLocal.addBackToStockActionLocation = dataLocal.userLocationName;
                }

                if (event.target.dataset.fieldlabel == 'didCauseDamage') {
                    if (event.target.value == 'Yes') {
                        dataLocal['isIssueResolved'] = 'No';
                        dataLocal['isIssueResolvedDisabled'] = true;

                        dataLocal['assesmentPossible'] = 'Later';
                        dataLocal['assesmentPossibleDisabled'] = true;

                        dataLocal['isPendingAssessment'] = true;
                        dataLocal['hasApprovalStatus'] = true;

                    } else {
                        dataLocal['isIssueResolved'] = '';
                        dataLocal['isIssueResolvedDisabled'] = false;

                        dataLocal['assesmentPossibleDisabled'] = false;

                        dataLocal['isPendingAssessment'] = false;
                        dataLocal['hasApprovalStatus'] = false;
                    }
                }

                if (event.target.dataset.fieldlabel == 'willSparePartCanFixIssue') {
                    if (event.target.value == 'Yes') {
                        dataLocal['disableCustomerOutcome'] = true;
                         //added by Sindhuri on 27/05/25 to avoid adding replacements for spare parts and actual Item
                        if(dataLocal['resolutionOutcome'] == 'Replacement Item'){
                            dataLocal['replacements'] = [];
                        }
                        dataLocal['resolutionOutcome'] = 'Replacement Part';
                    } else {
                        dataLocal['disableCustomerOutcome'] = false;
                        dataLocal['resolutionOutcome'] = '';
                        dataLocal['replacements'] = [];
                    }
                    dataLocal['isSpareAvailableInTable'] = undefined;
                }

                if (event.target.dataset.fieldlabel == 'isSpareAvailableInTable') {
                    if (event.target.value == 'No') {
                        dataLocal['willSparePartCanFixIssue'] = 'No';
                        dataLocal['disableCustomerOutcome'] = false;
                        // dataLocal['resolutionOutcome'] = '';
                    }
                }

                if (event.target.dataset.fieldlabel == 'resolutionOutcome'  && event.detail.value =='Money Back') {
                    dataLocal.refundType = this.erpData.refund_type != undefined ? this.erpData.refund_type == 'manual' ? 'Manual' : this.erpData.refund_type : 'Manual';
                    dataLocal.caseType = this.erpData.store_name == 'WHOLESALE ACCOUNTS' ? 'Commercial' :
                        this.erpData.store_name == '4WD SUPACENTRE (CAMPER TRAILERS)' || this.erpData.order_type == 'Camper Trailers' ?
                            'Camper Trailers' : 'Claims Department';
                    dataLocal.salesChannel = this.erpData.sales_channel ? this.erpData.sales_channel : '';
                    dataLocal.refundMethod = dataLocal.caseType == 'Commercial' ? 'Invoice Credit' :
                        dataLocal.refundType == 'integration' || dataLocal.salesChannel == 'Camper_Trailers' ? 'Integration' : 'Bank Account';
        
                    // this.updateDataObjToParent();
                }

                if (event.target.dataset.fieldlabel == 'resolutionOutcome'  && event.detail.value =='Replacement Item' && (this.userProfileName == 'Store Manager' || this.userProfileName == 'Store User')) {
                    dataLocal.shippingMethod = 'Pick Up';
                    dataLocal.shippingLocation = undefined;
                    this.locationValue = '';
                    if(dataLocal.shippingLocation == undefined || dataLocal.shippingLocation == '') {
                        dataLocal.shippingLocation = this.getIdByCity(this.shippingLocationList, this.userLocation);
                        this.shippingLocationSelected = this.getIdByCity(this.shippingLocationList, this.userLocation);
                        dataLocal.shippingLocationName = this.shippingLocationOptions.find(opt => opt.value === this.shippingLocationSelected)?.label;
                        this.locationValue = this.shippingLocationSelected;

                        if((this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') ) {
                            if(this.userProfileName == 'Store Manager' && this.userLocation.split(';').length>1){
                                dataLocal.disableLocationChange = false;
                            } else {
                                dataLocal.disableLocationChange = true;
                            }
                        } else {
                            dataLocal.disableLocationChange = false;
                        }
                    }
                }

            } else {
                dataLocal.selectedResolution = event.detail.value;
            }
        } else if (type == 'checkbox') {
            dataLocal[event.target.dataset.fieldlabel] = event.target.checked;
        }

        // if (this.validateSerialBatchValues(cardData[id]) && cardData[id].selectedResolution && (cardData[id].uploadedFiles.length > 0 || this.isCamper)) {
        //     cardData[id].isValid = true;
        // } else if (this.validateSerialBatchValues(cardData[id]) && cardData[id].selectedResolution && (cardData[id].uploadedFiles.length > 0 || this.isCamper)) {
        //     cardData[id].isValid = true;
        // } else {
        //     cardData[id].isValid = false;
        // }

        // this.cardData = cardData;
        // this.data = this.cardData[this.currentItemIndex];
        console.log('handle change ->', JSON.parse(JSON.stringify(dataLocal)));
        this.data = JSON.parse(JSON.stringify(dataLocal));
        this.updateDataObjToParent();
    }

     /* Parameters
        * value - selected ship to field.
    * Formats the Shipping To field.
    */
    filterShipToAddressByLabel(value) {
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let shippingToList = JSON.parse(JSON.stringify(this.shippingToList));
        console.log('shippingToList -> ' + JSON.stringify(shippingToList, null, 2));
        let filteredList = shippingToList.filter(function (el) {
            return el.label == value;
        });
        console.log('filteredList -> ' + JSON.stringify(filteredList, null, 2));

        if (filteredList.length > 0) {

            dataLocal.shipToAddress = {
                'buildingName': filteredList[0].buildingName,
                'street': filteredList[0].street,
                'suburb': filteredList[0].city,
                'state': filteredList[0].state,
                'postcode': filteredList[0].postcode,
                'country_code': filteredList[0].country,
                'firstName': filteredList[0].firstName,
                'lastName': filteredList[0].lastName,
                'email': filteredList[0].email,
                'phone': filteredList[0].phone
            };

            this.shipToAddress = {...dataLocal.shipToAddress};

        }

        this.data = {...JSON.parse(JSON.stringify(dataLocal))};
        return dataLocal.shipToAddress;
    }

    handleSKUSelection(event) {
        this.data.CP_Incorrecte_SKU__c = event.detail.mainField;
        this.data.CP_Incorrecte_SKU_Id__c = event.detail.id;
    }

    handleSKUSelectionForSameCategory(event) {
        this.data.CP_Replacement_SKU__c = event.detail.mainField;
        this.data.CP_Replacement_SKU_Id__c = event.detail.id;
    }

    handleSKURemoval(event) {
        this.data.CP_Incorrecte_SKU__c = '';
        this.data.CP_Incorrecte_SKU_Id__c = '';
    }

    handleSKURemovalForSameCategory(event) {
        this.data.CP_Replacement_SKU__c = '';
        this.data.CP_Replacement_SKU_Id__c = '';
    }

    handleProductSelectionChange(event) {
        this.data.selectProds = event.detail.data;
        // this.updateDataObjToParent();
    }


    updateDataObjToParent() {
        console.log('this.data'+JSON.stringify(this.data));
        this.dispatchEvent(new CustomEvent('datachange', {
            detail: {
                data: this.data,
                index: this.index
            }
        }));
    }

    handleDataChange(event) {
        this.data = JSON.parse(JSON.stringify(event.detail.data));
        this.updateDataObjToParent();
    }

    handleSectionToggle(event) {
        if (this.accordianOpenSection.length === 0) {
            this.accordianOpenSection = '';
        } else {
            this.accordianOpenSection = event.detail.openSections;
        }
    }

    /*
     * Parameters
        * event - onrowselection event from the lightning-datatable component.
     * Retrieval of selected claim product details.
    */
    handleRowSelection(event) {
        let selectedSPList = this.selectedSPList;


        if (event.detail.selectedRows) {

            event.detail.selectedRows.map((event) => {

                if (!selectedSPList.some(selectedSPList => selectedSPList.id == event.id)) {
                    selectedSPList.push({
                        'id': event.id,
                        'name': event.name,
                        'sku': event.sku
                    });
                }

            });

        }

        this.selectedCPList = selectedCPList;
    }


    // ATTACHMENT RELATED STUFF
    /*
    * Parameters
       * event - onupdateddata event from the claimFormFileUpload component.
    * Merging of uploaded files to the Card Details.
   */
    linkFilesToCard(event) {
        console.log('in event kap',event.detail);

        let updatedData = event.detail.value.updatedData;
        let uploadedFiles = event.detail.value.updatedUploadedFiles;
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        // let filterCard = cardData.filter(function (card) {
        //     return card.id == updatedData.id
        // });

        // let id = filterCard[0].id.substring(0, filterCard[0].id.indexOf(';'));
        dataLocal.uploadedFiles = updatedData.uploadedFiles;

        // if (this.validateSerialBatchValues(dataLocal) && dataLocal.selectedResolution) {
        //     dataLocal.isValid = true;
        // } else if (this.validateSerialBatchValues(dataLocal) && dataLocal.selectedResolution) {
        //     dataLocal.isValid = true;
        // } else {
        //     dataLocal.isValid = false;
        // }
        this.data = JSON.parse(JSON.stringify(dataLocal));
        console.log('1622 linkFilesToCard this.data -> ' + JSON.stringify(this.data, null, 2));
        // this.uploadedFiles = uploadedFiles;
        this.updateDataObjToParent();
    }

    /*
     * Parameters
        * event - onremove event from the lightning pill component.
     * Deletion of the selected file.
    */
    removeFile(event) {
        console.log('in cp item remove File');
        console.log('event - ', JSON.stringify(event.detail));
        let fileID = event.detail.name;
        let pillID = event.target.dataset.id;
        if (pillID != undefined) {
            let cardItemID = pillID.substring(0, pillID.indexOf(';'));

            this.deleteFile(fileID, cardItemID, true);
        }
        this.updateDataObjToParent();
    }

    /*
     * Parameters
        * event - onremove event from the lightning pill component.
     * Deletion of the selected file.
    */
    removeFileForProductIssue(event) {
        console.log('in cp item remove File');
        console.log('event - ', JSON.stringify(event.detail));
        let fileID = event.detail.id;

        this.deleteFile(fileID, null, true);
        this.updateDataObjToParent();
    }

    /*
     * Parameters
        * event - onremove event from the lightning pill component.
     * Deletion of the selected file.
    */
    removeFileForProductIssue(event) {
        console.log('in cp item remove File');
        console.log('event - ', JSON.stringify(event.detail));
        let fileID = event.detail.id;

        this.deleteFile(fileID, null, true);
        this.updateDataObjToParent();
    }

    /*
     * Parameters
        * fileID - Content Version ID of the Uploaded File.
     * Calling of deleteUploadedFiles Apex Method for the deletion of the uploaded file.
    */
    deleteFile(fileID, cardItemID, formatCard) {

        let cvIDList = [];

        if (formatCard) {

            this.isLoading = true;
            cvIDList.push(fileID);

        } else {

            this.modalLoading = true;
            this.disableModalButtons = {
                'confirm': true,
                'cancel': true
            };
            cvIDList = this.data.uploadedFiles;

        }

        deleteUploadedFiles({ cvIDList: cvIDList })
            .then((result) => {

                if (formatCard) {

                    // let cardData = JSON.parse(JSON.stringify(this.data));
                    let cardItem = JSON.parse(JSON.stringify(this.data.uploadedFiles));

                    for (var i = 0; i < cardItem.length; i++) {
                        if (cardItem[i].fileID === fileID) {
                            cardItem.splice(i, 1);
                        }
                    }

                    // if ((!this.isCamper && cardItem.length == 0) || !this.validateSerialBatchValues(this.data) || !this.data.selectedResolution) {
                    //     this.data.isValid = false;
                    // } else {
                    //     this.data.isValid = true;
                    // }

                    // this.cardData = cardData;
                    // this.data = this.data[this.currentItemIndex];
                    let dataLocal = JSON.parse(JSON.stringify(this.data));
                    dataLocal.uploadedFiles = cardItem;
                    this.data = {};
                    this.data = JSON.parse(JSON.stringify(dataLocal));
                    this.updateDataObjToParent();
                    this.isLoading = false;

                } else {

                    let pageName = 'orderProducts';
                    let erpData = this.erpData;

                    this.modalLoading = false;
                    this.disableModalButtons = {
                        'confirm': false,
                        'cancel': false
                    };
                    this.data.uploadedFiles = [];
                    this.sendNavigateEvent(pageName, erpData, null);

                }

            })
            .catch((error) => {
                let formattedError = ldsUtils.reduceErrors(error);
                // this.showError(formattedError);
            });

    }


    /*
     * Parameters
        * data - list of shipping to address.
     * Formats the Shipping To field.
    */
    formatShipToAddress(data) {

        let shippingToOptions = [];
        let shippingToList = [];

        if (data.length > 0) {

            for (let i = 0; i < data.length; i++) {

                shippingToOptions.push({
                    'label': data[i].MasterLabel,
                    'value': data[i].DeveloperName,
                });

                shippingToList.push({
                    'id': data[i].DeveloperName,
                    'label': data[i].MasterLabel,
                    'buildingName': data[i].Building_Name__c,
                    'street': data[i].Street__c,
                    'state': data[i].State__c,
                    'city': data[i].City__c,
                    'postcode': data[i].Postal_Code__c,
                    'country': data[i].Country__c,
                    'firstName': data[i].First_Name__c,
                    'lastName': data[i].Last_Name__c,
                    'email': data[i].Email__c,
                    'phone': data[i].Phone__c
                });

            }
        }

        this.shippingToOptions = shippingToOptions;
        this.shippingToList = shippingToList;

        this.filterShipToAddress(this.shippingToValue);

    }

    /* Parameters
        * value - selected ship to field.
    * Formats the Shipping To field.
    */
    filterShipToAddress(value) {
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let shippingToList = JSON.parse(JSON.stringify(this.shippingToList));
        let filteredList = shippingToList.filter(function (el) {
            return el.id == value;
        });

        if (filteredList.length > 0) {

            dataLocal.shipToAddress = {
                'buildingName': filteredList[0].buildingName,
                'street': filteredList[0].street,
                'suburb': filteredList[0].city,
                'state': filteredList[0].state,
                'postcode': filteredList[0].postcode,
                'country_code': filteredList[0].country,
                'firstName': filteredList[0].firstName,
                'lastName': filteredList[0].lastName,
                'email': filteredList[0].email,
                'phone': filteredList[0].phone
            };

            this.shipToAddress = { ...dataLocal.shipToAddress };

        }

        this.data = { ...JSON.parse(JSON.stringify(dataLocal)) };
        return dataLocal;
    }


    shipToAddOnChange(event) {
        let shipToAddress = this.shipToAddress;
        let street = [];

        street.push(event.target.street);

        shipToAddress.street = street;
        shipToAddress.suburb = event.target.city;
        shipToAddress.state = event.target.province;
        shipToAddress.postcode = event.target.postalCode;
        shipToAddress.country_code = event.target.country;

        // this.billToAddress.country_code = event.target.country;
        this.shipToAddress = { ...shipToAddress };

        this.data.shipToAddress = JSON.parse(JSON.stringify(this.shipToAddress));
    }


    /*
     * Parameters
        * event - onclick event from the lightning-button-icon component.
     * Navigate to the Knowledge Article Community Page.
    */
    navigateURL(event) {
        window.open(this.serialBatchUrl, "_blank");
    }

    
    validateBatchNumber(enteredValue, dataLocal) {
        isValidBatchNumberApex({ batchNumber: enteredValue+'', skuString: dataLocal.sku })
        .then(result => {
            let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
            if(inputField) {
                if (!result) {
                    inputField.setCustomValidity('Please enter a valid batch number.');
                    dataLocal.isBatchNumberCorrect = false;
                } else {
                    inputField.setCustomValidity('');
                    dataLocal.isBatchNumberCorrect = true;
                }

                inputField.reportValidity();
            }
            
        })
        .catch(error => {
            console.error('Error calling Apex method isValidBatchNumberApex:', error);
        });
    }

    get issueCategoryOptions() {
        console.log('259 this.userProfileName -> ' + JSON.stringify(this.userProfileName, null, 2));
        
        if (this.userProfileName == 'Warehouse') {
            return [
                {
                    label: 'Transit/Delivery Issue',
                    value: 'Transit/Delivery Issue'
                }
            ];
        }

        if(this.data != undefined && this.data.selectedIssueCategory != undefined && this.data.selectedIssueSubCategory != undefined 
            && this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Shipping') {
                return [
                    {
                        label: 'Transit/Delivery Issue',
                        value: 'Transit/Delivery Issue'
                    }
                ];
        }
        
        let returnOptions = [
            {
                label: '30 Day Change of Mind',
                value: '30 Day Change of Mind'
            },
            {
                label: 'Product Issue',
                value: 'Product Issue'
            },
            {
                label: 'Transit/Delivery Issue',
                value: 'Transit/Delivery Issue'
            },
            {
                label: 'Order Issue',
                value: 'Order Issue'
            }
        ];

        if(!(this.userProfileName == 'System Administrator' || this.userProfileName == 'Claim Manager' || this.userProfileName == 'Claim User' || this.userProfileName == 'Claims Agent' || this.userProfileName == 'Customer Service Agent')) { //added check for Customer Service agent
            returnOptions = returnOptions.filter(option => option.value !== 'Transit/Delivery Issue');
        }

        // Remove "Product Issue" if the product is not shipped
        if (this.data.shipmentStatus == 'Pending to Ship') {
            returnOptions = returnOptions.filter(option => option.value !== 'Product Issue');
        }

        if (this.cardData.length > 1) {
            returnOptions.push(
                {
                    label: 'Associated Product',
                    value: 'Associated Product'
                });
        }

        return returnOptions;
    }

    

  
    get isSerialRequired(){
        return this.data.serialRequired;    
    }
    get isBatchRequired(){
        return this.data.batchRequired;    
        // return false;
    }
}