    <template if:true={showAddAssessmentFormModal}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="max-width: 75rem !important; width:75% !important">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse"
                        title="Close" onclick={closeAssessmentFormModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close"
                            size="small"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">New Assessment Form</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-record-edit-form object-api-name="Assessment_Form__c">
                        <lightning-messages></lightning-messages>

                        <template lwc:if={isProductCategoryRefrigeration}>
                            <lightning-layout multiple-rows="true">
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <!-- Salesforce Claim # and Purchase Date -->
                                        <!-- <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Claim_Number__c"></lightning-input-field>
                                        </lightning-layout-item> -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Purchase_Date__c"
                                                value={erpData.order_date} readonly data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small" style="display: none;">
                                            <lightning-input-field 
                                                field-name="Selected_Record_Type__c" 
                                                value="Refrigeration" 
                                                data-groupname="assessmentFields">
                                            </lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Product_is_in_warranty__c"
                                                value={currentAssessmentForm.Product_is_in_warranty__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Physical_Condition__c"
                                                value={currentAssessmentForm.Physical_Condition__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Issue and Other -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Fridge_Issue__c"
                                                value={currentAssessmentForm.Fridge_Issue__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Other__c"
                                                value={currentAssessmentForm.Other__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Issue Occurs On and Date/Time Put on Power -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Fridge_Issue_Occurs_On__c"
                                                value={currentAssessmentForm.Fridge_Issue_Occurs_On__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">

                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Date_Time_Put_on_Power__c"
                                                value={currentAssessmentForm.Date_Time_Put_on_Power__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <!-- Temperature Set and Temperature Recorded -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Temperature_Set__c"
                                                value={currentAssessmentForm.Temperature_Set__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Temperature_Recorded__c"
                                                value={currentAssessmentForm.Temperature_Recorded__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Date/Time Recorded and Notes -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Date_Time_Recorded__c"
                                                value={currentAssessmentForm.Date_Time_Recorded__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Notes__c"
                                                value={currentAssessmentForm.Notes__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Cables -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="CIG_Cable__c"
                                                value={currentAssessmentForm.CIG_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Anderson_Cable__c"
                                                value={currentAssessmentForm.Anderson_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_Cable__c"
                                                value={currentAssessmentForm.AC_Cable__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Cover and Thermometer -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Cover__c"
                                                value={currentAssessmentForm.Cover__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Thermometer__c"
                                                value={currentAssessmentForm.Thermometer__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                            </lightning-layout>
                        </template>

                        <template lwc:elseif={isProductCategoryBushPower}>
                            <lightning-layout multiple-rows="true">
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <!-- Salesforce Claim # and Purchase Date -->
                                        <!-- <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Claim_Number__c"></lightning-input-field>
                                        </lightning-layout-item> -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Purchase_Date__c"
                                                value={erpData.order_date} readonly data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small" style="display: none;">
                                            <lightning-input-field 
                                                field-name="Selected_Record_Type__c" 
                                                value="Battery" 
                                                data-groupname="assessmentFields">
                                            </lightning-input-field>
                                        </lightning-layout-item>
                                        <!-- Product is in Warranty and Physical Condition -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Product_is_in_warranty__c"
                                                value={currentAssessmentForm.Product_is_in_warranty__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Physical_Condition__c"
                                                value={currentAssessmentForm.Physical_Condition__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Voltage on Arrival -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Voltage_on_Arrival__c"
                                                value={currentAssessmentForm.Voltage_on_Arrival__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- AC/DC Charge Start Time/Date and Finish Time/Date -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_DC_Charge_Start_Time_Date__c"
                                                value={currentAssessmentForm.AC_DC_Charge_Start_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="AC_DC_Charge_Finish_Time_Date__c"
                                                value={currentAssessmentForm.AC_DC_Charge_Finish_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Rated Capacity (AH) and Load Test Start Voltage -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Rated_Capacity_AH__c"
                                                value={currentAssessmentForm.Rated_Capacity_AH__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                                <lightning-layout-item size="6" padding="around-small">
                                    <lightning-layout multiple-rows="true">
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Start_Voltage__c"
                                                value={currentAssessmentForm.Load_Test_Start_Voltage__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Discharge Amps Set on DC Load -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Discharge_Amps_Set_on_DC_Load__c"
                                                value={currentAssessmentForm.Discharge_Amps_Set_on_DC_Load__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Load Test Start Time/Date and Finish Time/Date -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Start_Time_Date__c"
                                                value={currentAssessmentForm.Load_Test_Start_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Load_Test_Finish_Time_Date__c"
                                                value={currentAssessmentForm.Load_Test_Finish_Time_Date__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>

                                        <!-- Tested Capacity (AH) -->
                                        <lightning-layout-item size="12" padding="around-small">
                                            <lightning-input-field field-name="Tested_Capacity_AH__c"
                                                value={currentAssessmentForm.Tested_Capacity_AH__c} data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-layout-item>
                            </lightning-layout>
                        </template>
                        <template lwc:else>
                            <lightning-accordion allow-multiple-sections-open
                                active-section-name="faultReportSummary,includedAccessories,faultFoundRepaired,picturesOnFile,finalSpecificationCheck,repairInformation">

                                <lightning-accordion-section name="faultReportSummary" label="Fault Report Summary">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Fault_Report_Summary__c" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>

                                <lightning-accordion-section name="includedAccessories" label="Included Accessories">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Basket__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Handles__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Mains_IEC_Cable__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Instruction_Manual__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="DC_Lead__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Cover__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Other__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="faultFoundRepaired"
                                    label="Fault Found/Repaired/Recommendations">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Fault_Found_Repaired_Recommendations_Ref__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="picturesOnFile" label="Pictures On File">
                                    <lightning-layout horizontal-align="spread">
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Pictures_On_File_Of_Overall_Cosmetic__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="finalSpecificationCheck"
                                    label="Final Specification Check">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Temp_Offset_F1__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Temp_Offset_F2__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Empty_Fridge_Cool_Down_Time_Pass__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="CTR_Panel_Buttons_Display_OK__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Comp_Function_Delay_Start__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Cond_Fan_Function_Delay_Start__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="X12VDC_Operational_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="X24VDC_Operational_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Check_Interior_Light_Functions_OK__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Chk_Batt_Protect_Switch_Function__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Test_Batt_Protect_Set_To_Low__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="USB_Port_Functional_Test__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>


                                <lightning-accordion-section name="repairInformation" label="Repair Information">
                                    <lightning-layout multiple-rows>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field field-name="Repair_Completed_By__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                        <lightning-layout-item padding="around-small" size="6">
                                            <lightning-input-field
                                                field-name="Final_Inspection_And_Test_Completed_By__c"
                                                variant="label-inline" data-groupname="assessmentFields"></lightning-input-field>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                </lightning-accordion-section>

                            </lightning-accordion>
                        </template>

                    </lightning-record-edit-form>
                </div>
                <!-- test-->
                <footer class="slds-modal__footer">
                    <lightning-button variant="neutral" label="Cancel"
                        onclick={closeAssessmentFormModal}></lightning-button>
                    <lightning-button variant="brand" label="Save" onclick={saveAssessmentForm}
                        class="slds-m-horizontal_medium"></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>