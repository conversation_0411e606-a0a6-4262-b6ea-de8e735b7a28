/*
    @File Name          : claimFormOrderProducts.js
    <AUTHOR> <PERSON>
    @Last Modified By   : <PERSON>    @Last Modified On   : 02-20-2024
    @Modification Log   :
    ==============================================================================
        Ver         Date             Author      	Modification
    ==============================================================================
        1.0      10/29/2021          ICRUZ          Initial Version
        1.1      03/21/2022          ICRUZ          [CLA-252]
        1.2      04/18/2022          ICRUZ          [CLA-73]
        1.3      06/16/2022          ICRUZ          [CLA-298]
*/

import { LightningElement, api, wire, track } from 'lwc';

import { NavigationMixin } from 'lightning/navigation';

import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';               // import getObjectInfo and getPicklistValues from uiObjectInfoApi
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
 
import Claim_Product__c from '@salesforce/schema/Claim_Product__c';                           
import RETURNED_LOCATION_FIELD from '@salesforce/schema/Claim_Product__c.Returned_Location__c';

//USER OBJECT FIELDS
import USER_4WD_LOCATION_FIELD from '@salesforce/schema/User.X4WD_Locations__c';             // import User - 4WD Location Field Schema
import USER_PROFILE_NAME from '@salesforce/schema/User.Profile.Name';                       // import User - Profile Name
import USER_NAME from '@salesforce/schema/User.Name';                                       // import User - Name


import CASE_OBJECT from '@salesforce/schema/Case';                                          // import Case Object Schema
import CASE_REASON_FIELD from '@salesforce/schema/Case.Reason__c';                          // import Case - Reason Field Schema
import getProductArticles from '@salesforce/apex/LightningUtilities.getProductArticles';    // import getProductArticles method from LightningUtilities Apex Class
import getProductWarrenty from '@salesforce/apex/LightningUtilities.getProductWarrenty';    // import getProductArticles method from LightningUtilities Apex Class
import getClaimedShippingCostByOrderNumber from '@salesforce/apex/LightningUtilities.getClaimedShippingCostByOrderNumber';    // import getClaimedShippingCostByOrderNumber method from LightningUtilities Apex Class
import processOrderFromJson from '@salesforce/apex/FraudAndCancelOrderController.processOrderFromJson';    // import getClaimedShippingCostByOrderNumber method from LightningUtilities Apex Class
import * as ldsUtils from 'c/ldsUtils';                                                     // import ldsUtils component

import getClaimProductActions from '@salesforce/apex/RefundController.getClaimProductActions';
import handleRefund from '@salesforce/apex/ClaimProductActionHelper.handleRefund';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';


const TABLE_COLUMN = [                                                                      // value of the table columns
    {
        label: 'Item #',
        fieldName: 'itemNo',
        type: 'text',
        wrapText: true,
        initialWidth: 250,
        hideDefaultActions: true
    },
    {
        label: 'Item Name',
        fieldName: 'itemName',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true
    },
    {
        label: 'Batch Number',
        fieldName: 'batchNumber',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true
    },
    {
        label: 'Quantity Available to Claim',
        fieldName: 'availableQty',
        type: 'number',
        wrapText: true,
        initialWidth: 200,
        hideDefaultActions: true,
        cellAttributes: {
            alignment: 'center'
        }
    },
    {
        label: 'Quantity Claiming',
        fieldName: 'quantity',
        type: 'number',
        wrapText: true,
        initialWidth: 150,
        hideDefaultActions: true,
        cellAttributes: {
            alignment: 'center'
        }
    },
    {
        label: 'Is Packed?',
        fieldName: 'isPacked',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true,
    },
    {
        label: 'Is Shipped?',
        fieldName: 'shippingStatus',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true,
    },
    {
        label: 'In Warranty?',
        fieldName: 'inWarrenty',
        type: 'text',
        wrapText: true,
        hideDefaultActions: true,
    },
    {
        type: 'button-icon',
        initialWidth: 35,
        hideDefaultActions: true,
        typeAttributes: {
            iconName: 'utility:edit',
            name: 'editQty',
            class: {
                fieldName: 'showEditQtyBtn'
            }
        }
    }
];

export default class ClaimFormOrderProducts extends NavigationMixin(LightningElement ){

    @api erpData;                       // Order Data from the ERP.
    @api generatedJSON;                 // SObject Property of each objects.
    @api screenDetails;                 // Current Screen Details.
    @api userID;                        // Current User ID.
    @api itemNotReceivedCaseExists=false// Store the boolean value if Item not Received case already exists or not
    isLoading = true;                   // Toggle for showing or hiding the Lightning Spinner.
    displayKnowledge = false;           // Toggle for showing or hiding the related Knowledge Article.
    selectedSKU = '';                   // Value of the Selected SKU from the table.
    caseRTID = '';                      // Case Record Type ID.
    reasonOptions;                      // Reason Options.
    claimProductRTID = '';
    returnedLocationOptions = [];
    returnedLocationValue = '';
    returnedLocationDisabled = false;
    reasonValue = '';                   // Value of the Selected Reason.
    descriptionValue = '';              // Value of the Description.
    orderItemList = [];                 // Order Item List.
    orderProductList = [];              // Order Product List.
    tableData = [];                     // Data Table Values.
    tableColumn = TABLE_COLUMN;         // Data Table Columns.
    filteredData = [];                  // Filtered Data.
    items = [];                         // Cloned Data Table Values.
    draftValues = [];                   // Draft Values from the Lightning Datatable.
    expandedRows = [];                  // List of Expanded Rows from the Tree Grid.
    skuList;                            // List of SKUs.
    productArticlesList = [];           // List of Product Articles.
    page = 1;                           // Current Page Number.
    startingRecord = 1;                 // Starting Record.
    endingRecord = 0;                   // Ending Record.
    pageSize = 10;                      // Size of the data that will be displayed on the table
    totalRecountCount = 0;              // Total Number of Data.
    totalPage = 0;                      // Total Number of Page.
    displayError = false;               // Toggle for showing or hiding the Error Message.
    message = '';                       // Error Message.
    showEditQty = false;                // Toggle for showing or hiding the editClaimQuantity component.
    selectedRow;                        // Stores the data of the selected row from the Tree Grid.
    searchTerm = '';                    // Value of Search Term.
    disableModalButtons = {             // Toggle for enabling or disabling modal buttons for the editClaimQuantity component.
        'confirm': false,
        'cancel': false
    };
    commUserDetails = {                 // Value for the Commercial End User Details.
        'name': '',
        'email': '',
        'mobile': '',
        'address': '',
        'suppliedReplace': ''
    };
    commReplaceOptions = [              // End User Supplied Replacement Options.
        {
            label: 'Yes',
            value: 'true'
        },
        {
            label: 'No',
            value: 'false'
        }
    ];
    @track showModal = false;
    userProfileName = '';
    userName = '';
    isProfileAndLocationsValidated = false;
    isProfileAndReasonOptionValidated = false;
    isWarehouseOrStoreProfile = false;
    productWarrentyMap = {};
    existingClaimFreightAmount = 0;
    @track showCancelModal = false;
     @track isProcessing = false;
 @track claimProductActions = [];
    @track selectedRows = [];
   @track hasBankData;
    @track hasRequestedBankData;
    @track refundMethod;
    @track refundId;
    @track actionType;
    @track caseId;
    @track recordId;
    /*
     * No Parameters.
     * Formats the value of the Order ID.
    */
    get orderID() {
        return this.erpData.order_number ? this.erpData.order_number : '-';
    }

    /*
     * No Parameters.
     * Formats the value of the Email.
    */
    get email() {

        let email = '-';
        let data = this.erpData;

        if (data.hasOwnProperty('customer')) {
            email = data.customer.email;
        }

        return email;

    }

    /*
     * No Parameters.
     * Toggle for showing or hiding the Pagination buttons.
    */
    get displayPagination() {
        return this.items.length > this.pageSize ? true : false;
    }

    /*
     * No Parameters.
     * Toggle for showing or hiding the Commercial Claim End User Contact Details Section.
    */
    get displayCommSection() {
        return this.erpData.store_name == 'WHOLESALE ACCOUNTS' ? true : false;
    }

    /*
     * No Parameters.
     * Checks if tableData has value.
    */
    get hasTableData() {
        return this.tableData.length > 0 ? true : false;
    }

    /*
     * Parameters
        * objectApiName - SF Supported Objects based on the documentation.
     * This wire syntax is for the retrieval of the metadata for the Case Object.
    */
    @wire(getObjectInfo, { objectApiName: CASE_OBJECT })
    caseObjectInfo({ data, error }) {
        if (data) {
            this.caseRTID = data.defaultRecordTypeId;
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    /*
     * Parameters
        * recordTypeId - Case Record Type ID.
        * fieldApiName - API Name of Picklist Field.
     * This wire syntax is for the retrieval of the picklist values of the Reason Field.
    */
    @wire(getPicklistValues, { recordTypeId: '$caseRTID', fieldApiName: CASE_REASON_FIELD })
    caseReason({ data, error }) {
        if (data) {
            if(this.itemNotReceivedCaseExists == true) {
                this.reasonOptions = data.values.filter(option => option.value !== 'RTS - Returned to Sender');
            } else {
                this.reasonOptions = data.values;
            }
            this.retrieveSKUs();
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    @wire(getObjectInfo, { objectApiName: Claim_Product__c })
    claimProductObjectInfo({ error, data }) {
        if (data) {
            //this.objectInfo = data;
            this.claimProductRTID = data.defaultRecordTypeId;
        } else if (error) {
            console.error('Error fetching object info', error);
        }
    }

    /*
     * No Parameters.
     * This wire syntax is for the retrieval of the current user's 4WD Location and user data
    */
    @wire(getRecord, { recordId: '$userID', fields: [USER_4WD_LOCATION_FIELD, USER_PROFILE_NAME, USER_NAME]}) 
    getUserRecordData({ error, data }) {
        if (data) {
            this.userProfileName = getFieldValue(data, USER_PROFILE_NAME);
            this.userName =  getFieldValue(data, USER_NAME);
            if(this.userProfileName == 'Warehouse' ) {
                this.returnedLocationDisabled = true;
                this.returnedLocationValue = getFieldValue(data, USER_4WD_LOCATION_FIELD);
            }   
            this.isWarehouseOrStoreProfile = (this.userProfileName === 'Warehouse' || this.userProfileName.includes('Store') || this.userProfileName.includes('store')) ? true: false;
            
        } else if (error) {
            console.error('Error fetching Returned Location picklist values', error);
        }
    }

    @wire(getPicklistValues, { recordTypeId: '$claimProductRTID', fieldApiName: RETURNED_LOCATION_FIELD })
    claimProductReturnedLocation({ error, data }) {
        if (data) {
            
            this.returnedLocationOptions = data.values.map(item => ({
                label: item.label,
                value: item.value
            }));
 
        } else if (error) {
            console.error('Error fetching Returned Location picklist values', error);
        }
    }

    removeLocationsFromObjects(inputList) {
        return inputList.filter(item => item.value.endsWith(" DC"));
    }
      

    /*
     * No Parameters.
     * Retrieval of SKU Details from the Order Item List.
    */
    retrieveSKUs() {

        let erpData = JSON.parse(JSON.stringify(this.erpData));
        let skuList = [];

        if (erpData.hasOwnProperty('order_items')) {

            for (let i = 0; i < erpData.order_items.length; i++) {

                skuList.push(erpData.order_items[i].sku);

                if (erpData.order_items[i].hasOwnProperty('child_items')) {

                    if (erpData.order_items[i].child_items.length > 0) {

                        for (let j = 0; j < erpData.order_items[i].child_items.length; j++) {
                            skuList.push(erpData.order_items[i].child_items[j].sku);
                        }

                    }

                }

            }

        }

        this.skuList = [...new Set(skuList)];

    }

    /*
     * Parameters
        * selectedSKU - value of the selectedSKU variable.
     * This wire syntax is for the retrieval of the Related Knowledge Articles.
    */
    @wire(getProductArticles, { skuList: '$skuList' })
    getProductArticles({ data, error }) {
        if (data) {
            this.productArticlesList = data;
            this.formatTable();
            this.isLoading = false;
        } else if (error) {
            console.log(' >>> getProductArticles | error <<< ' + JSON.stringify(error, null, 2));
            this.isLoading = false;
        }
    }

    /*
     * No Parameters.
     * Formatting of the table to be displayed.
    */
    connectedCallback() {

        if (this.screenDetails) {

            this.orderItemList = this.screenDetails.orderItemList;
            this.orderProductList = this.screenDetails.orderProductList;
            console.log('371 this.screenDetails.items -> ' + JSON.stringify(this.screenDetails.items, null, 2));
            this.items = this.screenDetails.items;
            this.reasonValue = this.screenDetails.reasonValue;
            this.descriptionValue = this.screenDetails.descriptionValue;
            this.expandedRows = this.screenDetails.expandedRows;
            this.commUserDetails = this.screenDetails.commUserDetails;

            this.formatTable();
            this.filterTable();


        }

    }

    renderedCallback() {
        if(this.isProfileAndLocationsValidated == false && this.userProfileName != '' && this.returnedLocationOptions && this.returnedLocationOptions.length > 0) {
            if(this.userProfileName != 'Warehouse' && this.userProfileName != '') {
                // Filtering to get only objects where value ends with "DC"
                this.returnedLocationOptions = this.removeLocationsFromObjects(this.returnedLocationOptions);
            }
            this.isProfileAndLocationsValidated = true;
        }

        if(this.isProfileAndReasonOptionValidated == false && this.userProfileName != '' && this.reasonOptions && this.reasonOptions.length > 0) {
            let rtsAllowedProfiles = ['Warehouse', 'Finance', 'System Administrator'];
            if(!rtsAllowedProfiles.includes(this.userProfileName)) {
                this.reasonOptions = this.reasonOptions.filter(option => option.value !== 'RTS - Returned to Sender');
            }
            this.isProfileAndReasonOptionValidated = true;
        }
    }

    formatTable() {
        getProductWarrenty({skuList: this.skuList})
        .then(result => {
            console.log('getProductWarrenty result'+JSON.stringify(result));
            this.productWarrentyMap = JSON.parse(JSON.stringify(result));
            this.getClaimedShippingCostByOrderNumberFromApex();
        })
        .catch(error => {
            console.log('error'+JSON.stringify(error));
            this.getClaimedShippingCostByOrderNumberFromApex();
        });
    }

    getClaimedShippingCostByOrderNumberFromApex() {
        getClaimedShippingCostByOrderNumber({orderNumber: this.erpData.order_number})
        .then(result => {
            console.log('getClaimedShippingCostByOrderNumber result'+JSON.stringify(result));
            this.existingClaimFreightAmount = result;

            this.formatTableMain();
        })
        .catch(error => {
            console.log('error'+JSON.stringify(error));

            this.formatTableMain();
        });
    }
    


    /*
     * No Parameters.
     * Formats the data that will be displayed on the table.
    */
    formatTableMain() {

        let data = this.erpData;
        let tableData = this.items;
        let productArticlesList = this.productArticlesList;

        if (tableData.length == 0) {

            let expandedRows = [];

            if (data.hasOwnProperty('order_items')) {

                console.log('data.order_items -> ' + JSON.stringify(data.order_items, null, 2));
                let addedFreight = false;

                for (let i = 0; i < data.order_items.length; i++) {

                    let showKnowledgeBtn = 'slds-hide';
                    let showEditQtyBtn = 'slds-show';

                    if (productArticlesList.some(productArticlesList => productArticlesList.Product__r.SKU__c == data.order_items[i].sku)) {
                        showKnowledgeBtn = 'slds-show';
                    }

                    let inWarrenty = 'No';
                   // if(this.productWarrentyMap != undefined && Object.keys(this.productWarrentyMap).length > 0 && this.productWarrentyMap[data.order_items[i].sku].Warranty_Length__c != undefined) {
                   if (
                        this.productWarrentyMap &&
                        Object.keys(this.productWarrentyMap).length > 0 &&
                        this.productWarrentyMap[data.order_items[i].sku] &&
                        this.productWarrentyMap[data.order_items[i].sku].Warranty_Length__c !== undefined
                    ){
                        let warrentyInYears = this.productWarrentyMap[data.order_items[i].sku].Warranty_Length__c;
                        let expireDate = new Date(this.erpData.order_date);
                        expireDate.setFullYear(expireDate.getFullYear() + warrentyInYears);

                        if(new Date() <= expireDate)  {
                            inWarrenty = 'Yes'
                        }
                    }
                    
                    let shippedQty = data.order_items[i].refundable_qty_shipped;
                    let unShippedQty = data.order_items[i].refundable_qty_non_shipped;
                    let itemAddedToList = false;
                    console.log('AC:: shippedQty -> ' + JSON.stringify(shippedQty, null, 2));
                    console.log('AC:: unShippedQty -> ' + JSON.stringify(unShippedQty, null, 2));

                    if(shippedQty != undefined && shippedQty > 0 || data.order_items[i].sku == 'FREIGHT') {
                        let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i]));
                        orderItemLocal.isShipped = true;
                        orderItemLocal.isPacked = true;
                        orderItemLocal.inWarrenty = inWarrenty;                 
                        tableData.push({
                            'id': i + ';' + data.order_items[i].sku+';Shipped',
                            'quantity': null,
                            'itemNo': data.order_items[i].sku,
                            'itemName': data.order_items[i].name,
                            'availableQty': shippedQty,
                            'orderItemDetails': orderItemLocal,
                            'showEditQtyBtn': showEditQtyBtn,
                            'showKnowledgeBtn': showKnowledgeBtn,
                            'batchNumber': data.order_items[i].batch_number,
                            'inWarrenty': inWarrenty,
                            'shippingStatus': 'Yes',
                            'isPacked': 'Yes'
                        });

                        itemAddedToList = true;expandedRows.push(i + ';' + data.order_items[i].sku+';Shipped');
                    }

                    if(unShippedQty != undefined && unShippedQty > 0) {
                        let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i]));
                        orderItemLocal.isShipped = false;
                        let packedQuantity = data.order_items[i].wms_packed_qty;
                        let unpackedQuantity = data.order_items[i].qty - data.order_items[i].wms_packed_qty;
                        console.log('AC:: packedQuantity -> ' + JSON.stringify(packedQuantity, null, 2));
                        console.log('AC:: unpackedQuantity -> ' + JSON.stringify(unpackedQuantity, null, 2));

                        if(unpackedQuantity != undefined && unpackedQuantity > 0) {
                            orderItemLocal.isPacked = false;
                            orderItemLocal.inWarrenty = inWarrenty;
                            tableData.push({
                                'id': i + ';' + data.order_items[i].sku+';notShipped;unpacked',
                                'quantity': null,
                                'itemNo': data.order_items[i].sku,
                                'itemName': data.order_items[i].name,
                                'availableQty': unpackedQuantity,
                                'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                                'showEditQtyBtn': showEditQtyBtn,
                                'showKnowledgeBtn': showKnowledgeBtn,
                                'batchNumber': data.order_items[i].batch_number,
                                'inWarrenty': inWarrenty,
                                'shippingStatus': 'No',
                                'isPacked': 'No'
                            });

                            itemAddedToList = true;
                            expandedRows.push(i + ';' + data.order_items[i].sku+';notShipped;unpacked');
                        }
                        if(packedQuantity != undefined && packedQuantity > 0) {
                            orderItemLocal.isPacked = true;
                            orderItemLocal.readyForApproval = false;
                            orderItemLocal.inWarrenty = inWarrenty;                            
                            tableData.push({
                                'id': i + ';' + data.order_items[i].sku+';notShipped;packed',
                                'quantity': null,
                                'itemNo': data.order_items[i].sku,
                                'itemName': data.order_items[i].name,
                                'availableQty': packedQuantity,
                                'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                                'showEditQtyBtn': showEditQtyBtn,
                                'showKnowledgeBtn': showKnowledgeBtn,
                                'batchNumber': data.order_items[i].batch_number,
                                'inWarrenty': inWarrenty,
                                'shippingStatus': 'No',
                                'isPacked': 'Yes'
                            });
                            expandedRows.push(i + ';' + data.order_items[i].sku+';notShipped;packed');
                        }
                        // tableData.push({
                        //     'id': i + ';' + data.order_items[i].sku+';notShipped',
                        //     'quantity': null,
                        //     'itemNo': data.order_items[i].sku,
                        //     'itemName': data.order_items[i].name,
                        //     'availableQty': unShippedQty,
                        //     'orderItemDetails': orderItemLocal,
                        //     'showEditQtyBtn': showEditQtyBtn,
                        //     'showKnowledgeBtn': showKnowledgeBtn,
                        //     'batchNumber': data.order_items[i].batch_number,
                        //     'inWarrenty': inWarrenty,
                        //     'shippingStatus': 'No'
                        // });
                    }
                    if(data.order_items[i].child_items != undefined && data.order_items[i].child_items.length > 0) {
                        let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i]));
                        orderItemLocal.isShipped = false;
                        orderItemLocal.inWarrenty = inWarrenty;                        
                        tableData.push({
                            'id': i + ';' + data.order_items[i].sku+';notShipped',
                            'quantity': null,
                            'itemNo': data.order_items[i].sku,
                            'itemName': data.order_items[i].name,
                            'availableQty': 0,
                            'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                            'showEditQtyBtn': showEditQtyBtn,
                            'showKnowledgeBtn': showKnowledgeBtn,
                            'batchNumber': data.order_items[i].batch_number,
                            'inWarrenty': inWarrenty,
                            'shippingStatus': 'No'
                        });

                        itemAddedToList = true;
                        expandedRows.push(i + ';' + data.order_items[i].sku+';notShipped');
                    }
                    
                    if(itemAddedToList == false) {
                        if(data.order_items[i].sku == 'FREIGHT') {
                            if(this.existingClaimFreightAmount >= data.order_items[i].item_price) {
                                continue;
                            }
                        }

                        let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i]));
                        orderItemLocal.inWarrenty = inWarrenty;                        
                        tableData.push({
                            'id': i + ';' + data.order_items[i].sku+';notAllowed',
                            'quantity': null,
                            'itemNo': data.order_items[i].sku,
                            'itemName': data.order_items[i].name,
                            'availableQty': 0,
                            'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                            'showEditQtyBtn': showEditQtyBtn,
                            'showKnowledgeBtn': showKnowledgeBtn,
                            'batchNumber': data.order_items[i].batch_number,
                            'inWarrenty': inWarrenty,
                            'shippingStatus': ''
                        });
                        expandedRows.push(i + ';' + data.order_items[i].sku+';notAllowed');
                    }

                    if(data.order_items[i].sku == 'FREIGHT') {
                        tableData[i].availableQty = 1;
                        addedFreight = true;
                        tableData[i].orderItemDetails.item_price = tableData[i].orderItemDetails.item_price - this.existingClaimFreightAmount;
                        tableData[i].orderItemDetails.shipping_amount = tableData[i].orderItemDetails.shipping_amount - this.existingClaimFreightAmount;
                        
                    }

                    let childItems = [];
                    console.log('tableData.length -> ' + tableData.length)
                    console.log('tableData.length -> ', tableData);
                    
                    debugger;                    

                    if (data.order_items[i].child_items && data.order_items[i].child_items != undefined && data.order_items[i].child_items != []) {
                        console.log('data.order_items[i].sku -> ' + data.order_items[i].sku);
                        console.log('data.order_items[i].child_items.length -> ' + data.order_items[i].child_items.length);
                        if (data.order_items[i].child_items.length > 0) {

                            showEditQtyBtn = 'slds-hide';
                            let addedAtleastOneProduct = false;

                            for (let j = 0; j < data.order_items[i].child_items.length; j++) {

                                showKnowledgeBtn = 'slds-hide';

                                expandedRows.push(i + ';' + j + ';' + data.order_items[i].sku);

                                if (productArticlesList.some(productArticlesList => productArticlesList.Product__r.SKU__c == data.order_items[i].child_items[j].sku)) {
                                    showKnowledgeBtn = 'slds-show';
                                }
                                
                                console.log('data.order_items[i].child_items[j] -> ' + JSON.stringify(data.order_items[i].child_items[j], null, 2));

                                let inWarrentyChild = 'No';
                                if(this.productWarrentyMap != undefined && Object.keys(this.productWarrentyMap).length > 0 && this.productWarrentyMap[data.order_items[i].child_items[j].sku].Warranty_Length__c != undefined) {
                                    let warrentyInYearsChild = this.productWarrentyMap[data.order_items[i].child_items[j].sku].Warranty_Length__c;
                                    let expireDateChild = new Date(this.erpData.order_date);
                                    expireDateChild.setFullYear(expireDateChild.getFullYear() + warrentyInYearsChild);
            
                                    if(new Date() <= expireDateChild)  {
                                        inWarrentyChild = 'Yes'
                                    }
                                }

                                let shippedQty = data.order_items[i].child_items[j].refundable_qty_shipped;
                                let unShippedQty = data.order_items[i].child_items[j].refundable_qty_non_shipped;
                                let itemAddedToListChild = false;

                                if(shippedQty != undefined && shippedQty > 0) {
                                    let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i].child_items[j]));
                                    orderItemLocal.isShipped = true;
                                    orderItemLocal.isPacked = true;
                                    orderItemLocal.inWarrenty = inWarrentyChild;                                    
                                    childItems.push({
                                        'id': i + ';' + j + ';' + data.order_items[i].child_items[j].sku+';Shipped',
                                        'parentIndex': tableData.length-1,
                                        'quantity': null,
                                        'itemNo': data.order_items[i].child_items[j].sku,
                                        'itemName': data.order_items[i].child_items[j].name,
                                        'availableQty': shippedQty,
                                        'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                                        'showKnowledgeBtn': showKnowledgeBtn,
                                        'batchNumber': data.order_items[i].child_items[j].batch_number,
                                        'inWarrenty':inWarrentyChild,
                                        'shippingStatus': 'Yes',
                                        'isPacked': 'Yes'
                                    });
                                    addedAtleastOneProduct = true;
                                    itemAddedToListChild = true;
                                }

                                if(unShippedQty != undefined && unShippedQty > 0) {
                                    let packedQuantity = data.order_items[i].child_items[j].wms_packed_qty - data.order_items[i].child_items[j].wms_shipped_qty;
                                    let unpackedQuantity = data.order_items[i].child_items[j].qty - data.order_items[i].child_items[j].wms_packed_qty;

                                    let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i].child_items[j]));
                                    orderItemLocal.isShipped = false;
                                    if(packedQuantity != undefined && packedQuantity > 0) {
                                        orderItemLocal.isPacked = true;
                                        orderItemLocal.isShipped = false;
                                        orderItemLocal.readyForApproval = false;
                                        orderItemLocal.inWarrenty = inWarrentyChild;                                    
                                        childItems.push({
                                            'id': i + ';' + j + ';' + data.order_items[i].child_items[j].sku+';notShipped;packed',
                                            'parentIndex': tableData.length-1,
                                            'quantity': null,
                                            'itemNo': data.order_items[i].child_items[j].sku,
                                            'itemName': data.order_items[i].child_items[j].name,
                                            'availableQty': packedQuantity,
                                            'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                                            'showKnowledgeBtn': showKnowledgeBtn,
                                            'batchNumber': data.order_items[i].child_items[j].batch_number,
                                            'inWarrenty':inWarrentyChild,
                                            'shippingStatus': 'No',
                                            'isPacked': 'Yes'
                                        });
                                        itemAddedToListChild = true;
                                    }

                                    if(unpackedQuantity != undefined && unpackedQuantity > 0) {
                                        orderItemLocal.isPacked = false;
                                        orderItemLocal.isShipped = false;
                                        orderItemLocal.inWarrenty = inWarrentyChild;                                    
                                        childItems.push({
                                            'id': i + ';' + j + ';' + data.order_items[i].child_items[j].sku+';notShipped;unpacked',
                                            'parentIndex': tableData.length-1,
                                            'quantity': null,
                                            'itemNo': data.order_items[i].child_items[j].sku,
                                            'itemName': data.order_items[i].child_items[j].name,
                                            'availableQty': unpackedQuantity,
                                            'orderItemDetails': orderItemLocal,
                                            'showKnowledgeBtn': showKnowledgeBtn,
                                            'batchNumber': data.order_items[i].child_items[j].batch_number,
                                            'inWarrenty':inWarrentyChild,
                                            'shippingStatus': 'No',
                                            'isPacked': 'No'
                                        });
                                        itemAddedToListChild = true;
                                    }
                                    
                                    // childItems.push({
                                    //     'id': i + ';' + j + ';' + data.order_items[i].child_items[j].sku+';notShipped',
                                    //     'parentIndex': i,
                                    //     'quantity': null,
                                    //     'itemNo': data.order_items[i].child_items[j].sku,
                                    //     'itemName': data.order_items[i].child_items[j].name,
                                    //     'availableQty': unShippedQty,
                                    //     'orderItemDetails': orderItemLocal,
                                    //     'showKnowledgeBtn': showKnowledgeBtn,
                                    //     'batchNumber': data.order_items[i].child_items[j].batch_number,
                                    //     'inWarrenty':inWarrentyChild,
                                    //     'shippingStatus': 'No'
                                    // });
                                    addedAtleastOneProduct = true;
                                }
                               
                                if(itemAddedToListChild == false) {
                                    let orderItemLocal = JSON.parse(JSON.stringify(data.order_items[i].child_items[j]));
                                    orderItemLocal.inWarrenty = inWarrentyChild;                                    
                                    childItems.push({
                                        'id': i + ';' + j + ';' + data.order_items[i].child_items[j].sku+';NotAvailable',
                                        'parentIndex':tableData.length-1,
                                        'quantity': null,
                                        'itemNo': data.order_items[i].child_items[j].sku,
                                        'itemName': data.order_items[i].child_items[j].name,
                                        'availableQty': 0,
                                        'orderItemDetails': JSON.parse(JSON.stringify(orderItemLocal)),
                                        'showKnowledgeBtn': showKnowledgeBtn,
                                        'batchNumber': data.order_items[i].child_items[j].batch_number,
                                        'inWarrenty':inWarrentyChild,
                                        'shippingStatus': '',
                                        'isPacked': ''
                                    });
                                    addedAtleastOneProduct = true;
                                }

                            }

                            console.log('childItems -> ', childItems);
                            if(addedAtleastOneProduct == true && tableData != undefined && tableData.length > 0 && tableData[tableData.length-1] != undefined) {
                                tableData[tableData.length-1].showEditQtyBtn = showEditQtyBtn;
                                tableData[tableData.length-1]["_children"] = childItems;
                                tableData[tableData.length-1].availableQty = '';
                                tableData[tableData.length-1].inWarrenty = '';
                                tableData[tableData.length-1].shippingStatus = '';
                                tableData[tableData.length-1].isPacked = '';
                            }
                        }

                    }
                    console.log('tableData[i] -> ' + JSON.stringify(tableData[i], null, 2));
                }
                
                console.log('addedFreight -> ' + addedFreight);
                console.log('tableData -> ', JSON.stringify(tableData));
                console.log('this.existingClaimFreightAmount -> ' + this.existingClaimFreightAmount);
                
                
                if(data.shipping_amount != undefined && data.shipping_amount != '' && data.shipping_amount > 0 && addedFreight == false && 
                    this.existingClaimFreightAmount < data.shipping_amount) {
                    let showKnowledgeBtn = 'slds-hide';
                    let showEditQtyBtn = 'slds-show';
                    let orderItemDetails = {
                        "item_id": '',
                        "item_price": data.shipping_amount - this.existingClaimFreightAmount,
                        "item_cost": data.shipping_amount - this.existingClaimFreightAmount,
                        "sku": "FREIGHT",
                        "name": "FREIGHT",
                        "qty": 1,
                        "qty_shipped": 0,
                        "row_total_price": data.shipping_amount - this.existingClaimFreightAmount,
                        "shipping_references": "[]",
                        "status": "",
                        "total_loyalty_value": "",
                        "total_discount": "",
                        "total_tax": "",
                        "total_price": "",
                        "total_cost": "",
                        "item_loyalty_value": "",
                        "item_tax": "",
                        "item_discount": "",
                        "qty_returned": "",
                        "product_type": "",
                        "is_virtual": "",
                        "is_pickedup": "",
                        "note": "",
                        "id": "",
                        "batch_number": "",
                        "wms_packed_qty": 0,
                        "wms_returned_qty": 0,
                        "wms_shipped_qty": 0,
                        "wms_refunded_qty": 0,
                        "refundable_qty": "0",
                        "upack_required_qty": "0"
                    }
                    
                    let freightObj = {
                        'id': data.order_items.length+1 + ';' + 'FREIGHT',
                        'quantity': null,
                        'itemNo': 'FREIGHT',
                        'itemName': 'FREIGHT',
                        'availableQty': Number(1),
                        'orderItemDetails': orderItemDetails,
                        'showEditQtyBtn': showEditQtyBtn,
                        'showKnowledgeBtn': showKnowledgeBtn
                    }
                    tableData.push(freightObj);
                    console.log('Added freight obj - ', freightObj);
                }
            }

            this.expandedRows = expandedRows;

        }

        console.log('843 tableData -> ' + JSON.stringify(tableData, null, 2));

        this.items = tableData;

        this.tableData = this.items.slice(0, this.pageSize);
        this.endingRecord = this.pageSize;
        this.filterTable();

    }

    /*
     * Parameters
        * event - onrowaction event from the lightning datatable.
     * Displays the Related Knowledge Article.
    */
    handleRowAction(event) {

        let actionName = event.detail.action.name;
        let row = event.detail.row;

        if (actionName == 'editQty') {
            this.selectedRow = row;
            this.showEditQty = true;
        } else if (actionName == 'showKnowledge') {
            this.selectedSKU = row.itemNo;
            this.displayKnowledge = true;
        }

    }

    /*
     * Parameters
        * event - onchange event from the lightning input, combobox, textarea components.
     * Assignment of reasonValue, descriptionValue and  variable.
    */
    handleChange(event) {

        let id = event.target.dataset.id;
        let type = event.target.dataset.type;
        let value = '';

        if (type == 'input' || type == 'textarea') {
            value = event.target.value;
        } else if (type == 'combobox' || type == 'radio') {
            value = event.detail.value;
        }

        switch (id) {

            case 'reason':
                this.reasonValue = value;
                break;
            case 'description':
                this.descriptionValue = value;
                break;
            case 'commName':
                this.commUserDetails.name = value;
                break;
            case 'commEmail':
                this.commUserDetails.email = value;
                break;
            case 'commMobile':
                this.commUserDetails.mobile = value;
                break;
            case 'commAddress':
                this.commUserDetails.address = value;
                break;
            case 'commReplacement':
                this.commUserDetails.suppliedReplace = value;
                break;
            case 'searchTerm':

                this.searchTerm = value;

                if (!value) {
                    this.page = 1;
                    this.filterTable();
                }

                break;

        }

    }
  
    addAllOrderItems() {
    
        let updatedItems = this.items.map(item => {

            if (item.showEditQtyBtn === 'slds-show') {
                item = { ...item, quantity: item.availableQty };
            }
    
            if (item._children && item._children.length > 0) {
                let updatedChildren = item._children.map(child => {
                    return { ...child, quantity: child.availableQty };
                    
                });
                return { ...item, _children: updatedChildren };
            }
    
            return item;
        });
    
        this.items = [...updatedItems];
        this.tableData = [...this.items];
    
    }
    cancelAllFraudOrders(){

    }
    
    handleReturnedLocationChange(event) {
        this.returnedLocationValue = event.detail.value;
    }

    /*
     * Parameters
        * event - onkeydown event of the lightning input.
     * Calling of handleClick method upon pressing enter.
    */
    handleOnKeyDown(event) {

        let buttonEvent = {
            'target': {
                'dataset': {
                    'id': 'search'
                }
            }
        };

        if (event.keyCode === 13) {
            this.handleClick(buttonEvent);
        }

    }

    /*
     * No Parameters.
     * Performs a validation on the table.
    */
    validateItems() {

        try {

            let items = JSON.parse(JSON.stringify(this.items));
            let parentItems = [];
            let childItems = [];
            let parentCounter = 0;
            let childCounter = 0;
            let isValid = true;

            if (items.length > 0) {

                parentItems = items.filter(obj => !obj.hasOwnProperty('_children'));
                let parentWithChildItems = items.filter(obj => obj.hasOwnProperty('_children'));

                if (parentWithChildItems.length > 0) {
                    parentWithChildItems.map(obj => {
                        if (obj.hasOwnProperty('_children')) {
                            for (let i = 0; i < obj['_children'].length; i++) {
                                childItems.push(obj['_children'][i]);
                            }
                        }
                    });
                }

            }

            if (parentItems.length > 0) {

                parentCounter = parentItems.filter(obj => {
                    return obj.quantity == null || obj.quantity == '' || obj.quantity == 0
                }).length;

            }

            if (childItems.length > 0) {

                childCounter = childItems.filter(obj => {
                    return obj.quantity == null || obj.quantity == '' || obj.quantity == 0
                }).length;

            }

            if ((parentItems.length == parentCounter) && (childItems.length == childCounter)) {
                isValid = false;
            }

            return isValid;

        } catch (error) {
            this.showError(error);
        }

    }

    closeModalPopup()
    {
        this.showModal = false;
    }
    cancelModal()
    {
        this.showModal = false;
    }
    
    /*
     * Parameters
        * event - onclick event of the Submit button.
     * Performs Table Validation.
     * Sending of event back to the parent component.
    */
    handleClick(event) {
        // this.showModal = true;

        try {

            let buttonName = event.target.dataset.id;

            if (buttonName == 'search') {
                this.page = 1;
                this.filterTable();
            } else if (buttonName == 'next') {

                this.message = '';
                this.displayError = false;

                let hasReason = ldsUtils.validateForm(this, 'lightning-combobox');
                let hasDescription = ldsUtils.validateForm(this, 'lightning-textarea');
                let validInput = ldsUtils.validateForm(this, 'lightning-input');
                let validRadio = ldsUtils.validateForm(this, 'lightning-radio-group');

                if (hasReason && hasDescription && validInput && validRadio) {

                    let isValid = this.validateItems();
                    
                    if (!isValid) {

                        let message = 'Please select at least one item to claim.';
                        this.showError(message);
                        return;

                    }

                    if(this.reasonValue == 'RTS - Returned to Sender') {
                        this.showModal = true;
                        return;
                    }
                            
                    this.prepareJSOAndSendNavigation();
                    

                } else {
                    ldsUtils.scrollTo(this, 'orderProducts');
                }

            }

        } catch (error) {
            this.showError(error);
        }

    }

    handleSaveAction(event) {
        this.prepareJSOAndSendNavigation();
    }

    prepareJSOAndSendNavigation() {
        let pageName = 'orderItems';
                    let clonedTableItems = JSON.parse(JSON.stringify(this.items));
                    let currentdate = new Date();
                    let dateTime = currentdate.getDate() + "/" + (currentdate.getMonth() + 1) + "/" +
                        currentdate.getFullYear() + " @ " + currentdate.getHours() + ":" +
                        currentdate.getMinutes() + ":" + currentdate.getSeconds() + ':' +
                        currentdate.getMilliseconds();

                    this.orderItemList = [];
                    this.orderProductList = [];

                    if (clonedTableItems.length > 0) {

                        for (let i = 0; i < clonedTableItems.length; i++) {

                            let tableItem = clonedTableItems[i];
                            let randomNumber = Math.random();
                            let uniqueID = i + '-' + randomNumber + '-' + dateTime;
                            let orderItemList = this.orderItemList;
                            let orderProductList = this.orderProductList;

                            if (tableItem.hasOwnProperty('_children')) {

                                let tableChildItems = tableItem['_children'];

                                orderProductList.push({
                                    'qty': tableItem.orderItemDetails.qty,
                                    'data': tableItem.orderItemDetails,
                                    'uniqueID': uniqueID
                                });

                                for (let j = 0; j < tableChildItems.length; j++) {

                                    let tableChildItem = tableChildItems[j];
                                    let tableChildQty = tableChildItem.quantity;

                                    if (tableChildQty != '' || tableChildQty != null || tableChildQty != 0) {

                                        for (let z = 0; z < tableChildQty; z++) {

                                            orderItemList.push({
                                                'id': tableChildItem.id,
                                                'qty': tableChildQty,
                                                'data': tableChildItem.orderItemDetails,
                                                'uniqueID': uniqueID,
                                                'isChild': true
                                            });

                                        }

                                    }

                                }

                            } else {

                                let tableItemQty = tableItem.quantity;

                                orderProductList.push({
                                    'qty': tableItem.orderItemDetails.qty,
                                    'data': tableItem.orderItemDetails,
                                    'uniqueID': uniqueID
                                });

                                if (tableItemQty != '' || tableItemQty != null || tableItemQty != 0) {

                                    for (let j = 0; j < tableItemQty; j++) {

                                        orderItemList.push({
                                            'id': tableItem.id,
                                            'qty': tableItemQty,
                                            'data': tableItem.orderItemDetails,
                                            'uniqueID': uniqueID,
                                            'isChild': false
                                        });

                                    }

                                }

                                this.orderItemList = orderItemList;
                                this.orderProductList = orderProductList;

                            }

                        }

                        this.sendNavigateEvent(pageName, this.erpData);
                        
                    }
    }

    /*
     * Parameters
        * event - onclick event from the editClaimQuantity component .
     * Retrieves the inputted quantity of the selected item from the tree grid.
    */
    handleEditClick(event) {

        let eventStatus = event.detail.value.status;

        if (eventStatus == 'confirm') {

            let updatedTableData = event.detail.value.updatedTableData;

            this.items = updatedTableData;

            this.filterTable();
            this.displayRecordPerPage(this.page);

        }

        this.showEditQty = false;

    }

    /*
     * Parameters
        * event - onclosemodal event from the knowledgeLinks component .
     * Displays the Related Knowledge Article.
    */
    closeModal(event) {
        this.displayKnowledge = false;
        this.selectedSKU = '';
    }

    /*
     * No Parameters.
     * Navigates to the previous set of data in the table.
    */
    previousHandler() {

        if (this.page > 1) {
            this.page = this.page - 1; //decrease page by 1
            this.displayRecordPerPage(this.page);
        }

    }

    /*
     * No Parameters.
     * Navigates to the next set of data in the table.
    */
    nextHandler() {

        if ((this.page < this.totalPage) && (this.page !== this.totalPage)) {
            this.page = this.page + 1; //increase page by 1
            this.displayRecordPerPage(this.page);
        }

    }

    /*
     * Parameters
        * page - value of the page variable.
     * Displays the records page by page.
    */
    displayRecordPerPage(page) {

        this.startingRecord = ((page - 1) * this.pageSize);
        this.endingRecord = (this.pageSize * page);
        this.endingRecord = (this.endingRecord > this.totalRecountCount) ? this.totalRecountCount : this.endingRecord;

        if (this.searchTerm && this.filteredData.length > 0) {
            this.tableData = this.filteredData.slice(this.startingRecord, this.endingRecord);
        } else {
            this.tableData = this.items.slice(this.startingRecord, this.endingRecord);
        }

        this.startingRecord = this.startingRecord + 1;

        ldsUtils.scrollTo(this, 'tableDetails');

    }

    /*
     * Parameters
        * pageName - name of the screen that will be displayed.
        * data     - value of the erpData variable.
     * Triggers a navigatepage event to the parent component.
    */
    sendNavigateEvent(pageName, data) {

        // data = this.addDummyData(data);

        let thirdScreenDetails = {
            'expandedRows': this.expandedRows,
            'orderItemList': this.orderItemList,
            'orderProductList': this.orderProductList,
            'items': this.items,
            'reasonValue': this.reasonValue,
            'descriptionValue': this.descriptionValue,
            'commUserDetails': this.commUserDetails
        };

        let value = {
            'pageName': pageName,
            'erpData': data,
            'thirdScreenDetails': thirdScreenDetails,
            'bypassFourthScreen': this.showModal,
            'returnedLocationValue': this.returnedLocationValue,
            'userName': this.userName
        };

        let navigateChange = new CustomEvent('navigatepage', {
            detail: { value }
        });

        this.dispatchEvent(navigateChange);

    }

    /*
     * Parameters
        * message - error message.
     * Showing of Error Message.
    */
    showError(message) {

        this.message = message;
        this.displayError = true;
        this.isLoading = false;

        ldsUtils.scrollTo(this, 'orderProducts');

    }

    /*
     * No Parameters.
     * Filtering of Order Items Table.
    */
    filterTable() {

        let data = JSON.parse(JSON.stringify(this.items, null, 2));
        let filterValue = this.searchTerm;
        let updatedData = [];

        if (!filterValue) {
            updatedData = data;
        } else {

            let updatedDataMap = {};

            if (data.length > 0) {

                for (let i = 0; i < data.length; i++) {

                    let origData = data[i];

                    if (origData['itemNo'].toLowerCase().includes(filterValue.toLowerCase()) ||
                        origData['itemName'].toLowerCase().includes(filterValue.toLowerCase())) {
                        updatedDataMap[origData['itemNo']] = origData;
                    }

                    if (origData.hasOwnProperty('_children')) {

                        let childData = origData['_children'];

                        for (let j = 0; j < childData.length; j++) {

                            if (childData[j]['itemNo'].toLowerCase().includes(filterValue.toLowerCase()) ||
                                childData[j]['itemName'].toLowerCase().includes(filterValue.toLowerCase())) {
                                if (!updatedDataMap.hasOwnProperty(origData['itemNo'])) {
                                    updatedDataMap[origData['itemNo']] = origData;
                                }
                            }

                        }

                    }

                }

                if (Object.keys(updatedDataMap).length > 0) {
                    updatedData = Object.values(updatedDataMap);
                } else {
                    this.page = updatedData.length;
                }

            }

        }

        this.filteredData = updatedData;
        this.totalRecountCount = updatedData.length;
        this.totalPage = Math.ceil(this.totalRecountCount / this.pageSize);
        this.tableData = updatedData.slice(0, this.pageSize);
        this.endingRecord = this.pageSize;

    }
     showCancelConfirmationModal() {
        this.showCancelModal = true;
      
    }
     handleModalCancel() {
        this.showCancelModal = false;
    }
    confirmCancelOrders() {
         this.isProcessing = true;
      
        
        // 🛑 Run actual cancellation logic here
        this.cancelAllFraudOrders();
    }


cancelAllFraudOrders() {
    console.log('ERP Data:', JSON.stringify(this.erpData));
this.isProcessing = true;
    // Ensure erpData exists
    if (!this.erpData || Object.keys(this.erpData).length === 0) {
        console.error('ERP data is missing or empty:', this.erpData);
        // this.isProcessing = false;
        return;
    }

    // Convert object to JSON string
    const payload = JSON.stringify(this.erpData);
    console.log('Sending JSON to Apex:', payload);

    processOrderFromJson({ jsonStr: payload })
        .then(caseId => {
                console.log('✅ Case Id returned:', caseId);
                //this.isProcessing = false;
               // this.showCancelModal = false;
                this.recordId = caseId;
            this.loadClaimProductActions();
            
            })
            .catch(error => {
                console.error('❌ Error processing order:', error);
               // this.isProcessing = false;
            });
}
        loadClaimProductActions() {
            this.loaded = false; // show spinner if needed
            console.log(' this.recordId@@@@@@'+ this.recordId);
        getClaimProductActions({ 
                caseId: this.recordId, 
                actionType: 'Money Back;Fraud payment' 
            })
            .then(data => {
                if (data) {
                    this.claimProductActions = data.map(item => {
                        this.refundMethod = item.Case__r.Order_Id__r.Refund_Type__c;
                        this.hasBankData = item.Case__r.Bank_Detail_Request_Completed__c;
                        this.hasRequestedBankData = item.Case__r.Bank_Detail_Request_Date__c;
                        this.actionType = item.Action_Type__c;
                        console.log('this.actionType@@@@ ', this.actionType);

                        return {
                            ...item,
                            actionRecordLink: '/' + item.Id,
                            claimProductRecordLink: '/' + item.Claim_Product__c,
                            claimProductName: item.Claim_Product__r ? item.Claim_Product__r.Name : '',
                            productSKU: item.Claim_Product__r ? item.Claim_Product__r.SKU__c : '',
                            productId: item.Claim_Product__r && item.Claim_Product__r.Product_Id__r ? item.Claim_Product__r.Product_Id__r.Name : ''
                        };
                    });
                    this.selectedRows = this.claimProductActions.map(row => row.Id);
                    this.handleProcess();
                }
                this.loaded = true;
            })
            .catch(error => {
                this.showToast('Error', error.body ? error.body.message : error.message, 'error');
                this.loaded = true;
               //  this.isProcessing = false;
                this.showCancelModal = false;
                
            });

        }
        handleProcess() {
                if (this.selectedRows.length === 0) {
                    //this.isProcessing = false;
                    this.showToast('Warning', 'Please select at least one action to process.', 'warning');
                    return;
                }
                this.loaded = false;
                handleRefund({ cpaIds: this.selectedRows })
                    .then((result) => {
                        console.log('result -> ' + JSON.stringify(result));
                        this.refundId = result.Id;
                        // this.isProcessing = false;

                        this.showToast('Success', 'Refund processed successfully', 'success');
                        this.loaded = true;
                        this[NavigationMixin.Navigate]({
                            type: "standard__recordPage",
                            attributes: {
                            recordId: this.refundId,
                            objectApiName: "Refund__c",
                            actionName: "view"
                            }
                        });
                    })
                    .catch(error => {
                        this.loaded = true;
                        this.showToast('Error', error.body.message, 'error');
                    });
            }
            showToast(title, message, variant) {
                const event = new ShowToastEvent({
                    title: title,
                    message: message,
                    variant: variant,
                });
                this.dispatchEvent(event);
            }
            get showCancelFraudButton() {
    return this.userProfileName === 'Claim Manager' || this.userProfileName === 'System Administrator';
}
}